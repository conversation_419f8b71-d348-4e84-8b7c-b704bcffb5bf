import 'dart:async';
import 'package:flutter/material.dart';

/// Stub implementation of RazorpayWebService for mobile platforms
/// This provides the same interface but throws appropriate errors when used
class RazorpayWebService {
  static final RazorpayWebService _instance = RazorpayWebService._internal();
  factory RazorpayWebService() => _instance;
  RazorpayWebService._internal();

  /// Initialize the web service (no-op on mobile)
  void initialize() {
    print('RazorpayWebService stub - not available on mobile platforms');
  }

  /// Start payment using web SDK (throws error on mobile)
  Future<Map<String, dynamic>> startWebPayment({
    required String orderId,
    required String keyId,
    required double amount,
    required String currency,
    required String name,
    required String description,
    required String userEmail,
    required String userPhone,
    required String userName,
  }) async {
    throw UnsupportedError('Web payment not supported on mobile platforms');
  }

  /// Process wallet top-up (throws error on mobile)
  Future<Map<String, dynamic>> processWebWalletTopUp({
    required BuildContext context,
    required double amount,
    required String currency,
    required String userEmail,
    required String userPhone,
    required String userName,
    String? transactionId,
  }) async {
    throw UnsupportedError(
      'Web wallet top-up not supported on mobile platforms',
    );
  }

  /// Test payment functionality (no-op on mobile)
  void testWebPayment() {
    print('Test payment not available on mobile platforms');
  }

  /// Dispose method (no-op on mobile)
  void dispose() {
    // No-op for mobile platforms
  }
}

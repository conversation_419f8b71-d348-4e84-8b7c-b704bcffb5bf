import { NextRequest, NextResponse } from 'next/server'
import { getAdminToken } from '@/lib/auth'

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3001'

export async function GET(request: NextRequest) {
  try {
    const token = await getAdminToken(request)
    const { searchParams } = new URL(request.url)
    
    // Forward query parameters to backend
    const queryString = searchParams.toString()
    const url = `${BACKEND_URL}/support/admin/chat/sessions${queryString ? `?${queryString}` : ''}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token,
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `Backend responded with status: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching chat sessions:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch chat sessions' },
      { status: 500 }
    )
  }
}

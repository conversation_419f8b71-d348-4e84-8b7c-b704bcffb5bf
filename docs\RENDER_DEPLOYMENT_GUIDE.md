# WiggyZ Backend Deployment to Render.com

This guide provides step-by-step instructions for deploying the WiggyZ backend to Render.com.

## Prerequisites

- Render.com account
- GitHub repository access
- Supabase project credentials
- Razorpay credentials (for payment processing)

## Deployment Steps

### 1. Create Web Service on Render

1. **Access Render Dashboard**
   - Go to [https://dashboard.render.com](https://dashboard.render.com)
   - Sign in to your account

2. **Create New Web Service**
   - Click "New +" → "Web Service"
   - Connect your GitHub account if not already connected
   - Select repository: `mdwasim1340/wiggyz2`
   - Click "Connect"

3. **Configure Service Settings**
   ```
   Name: wiggyz-backend
   Runtime: Node
   Region: Oregon (or your preferred region)
   Branch: master
   Root Directory: backend
   Build Command: npm install && npm run build
   Start Command: npm start
   Plan: Starter (Free tier available)
   ```

### 2. Environment Variables Configuration

Add the following environment variables in the Render dashboard:

```bash
# Server Configuration
NODE_ENV=production
PORT=10000

# Supabase Configuration
SUPABASE_URL=https://johjwarjusfahxidemjd.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F77CeDDNeQqRjhhUjhcB_322RLaP4pBx6-2L0VZ7gJY

# JWT Configuration
JWT_REFRESH_SECRET=wiggyz_refresh_secret_change_in_production_render_2025

# Razorpay Configuration (Update with your actual credentials)
RAZORPAY_KEY_ID=your_razorpay_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_key_secret_here

# Optional: Logging and Rate Limiting
LOG_LEVEL=info
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
```

### 3. Deploy the Service

1. Click "Create Web Service"
2. Render will automatically start the deployment process
3. Monitor the build logs for any errors
4. Wait for deployment to complete (usually 5-10 minutes)

### 4. Verify Deployment

Once deployed, your service will be available at:
```
https://wiggyz-backend.onrender.com
```

Test the following endpoints:

1. **Health Check**
   ```bash
   curl https://wiggyz-backend.onrender.com/health
   ```
   Expected response: `{"status":"ok"}`

2. **API Base**
   ```bash
   curl https://wiggyz-backend.onrender.com/api/v1/
   ```
   Expected response: API information without authentication errors

### 5. Update Flutter Configuration

The Flutter app configuration has been automatically updated to use the Render URL:

**File**: `frontend/lib/core/api/api_config.dart`
```dart
static String get baseUrl {
  return 'https://wiggyz-backend.onrender.com/api/v1';
}
```

### 6. Update Admin Panel Configuration

Update the admin panel configuration to use the new Render URL:

**File**: `wiggyz_admin/lib/config/api.ts`
```typescript
static get baseUrl(): string {
  return process.env.NEXT_PUBLIC_API_URL || 'https://wiggyz-backend.onrender.com/api/v1';
}
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check that all dependencies are listed in package.json
   - Verify TypeScript compilation succeeds locally
   - Review build logs in Render dashboard

2. **Environment Variable Issues**
   - Ensure all required environment variables are set
   - Check for typos in variable names
   - Verify Supabase credentials are correct

3. **Health Check Failures**
   - Verify the `/health` endpoint is implemented
   - Check server startup logs
   - Ensure the server is listening on the correct port (10000)

### Monitoring and Logs

- Access logs through the Render dashboard
- Monitor service health and performance
- Set up alerts for service downtime

## Benefits of Render Deployment

1. **Public Accessibility**: Unlike Vercel, Render services are publicly accessible without authentication
2. **Persistent Connections**: Better suited for real-time features
3. **Environment Isolation**: Proper production environment setup
4. **Automatic SSL**: HTTPS enabled by default
5. **Git Integration**: Automatic deployments on code changes

## Next Steps

1. Test the mobile app connectivity with the new Render URL
2. Update any hardcoded URLs in the codebase
3. Configure custom domain if needed
4. Set up monitoring and alerting
5. Consider upgrading to a paid plan for better performance

## Security Notes

- The JWT_REFRESH_SECRET has been updated for production
- Ensure Razorpay credentials are properly secured
- Consider using Render's secret management for sensitive data
- Regularly rotate secrets and API keys

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/features/wallet/providers/wallet_provider.dart';
import 'package:wiggyz_app/services/razorpay_service.dart';
import 'package:wiggyz_app/services/razorpay_web_service_interface.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:uuid/uuid.dart';

class PaymentTestScreen extends StatefulWidget {
  const PaymentTestScreen({super.key});

  @override
  State<PaymentTestScreen> createState() => _PaymentTestScreenState();
}

class _PaymentTestScreenState extends State<PaymentTestScreen> {
  final TextEditingController _amountController = TextEditingController(
    text: '100',
  );
  late RazorpayService _razorpayService;
  late RazorpayWebService _webService;
  final Uuid _uuid = const Uuid();
  bool _isProcessing = false;
  String _status = 'Ready to test';
  List<String> _logs = [];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  void _initializeServices() {
    try {
      _razorpayService = RazorpayService();
      _razorpayService.initialize();

      if (kIsWeb) {
        _webService = RazorpayWebService();
        _webService.initialize();
      }

      _addLog('Services initialized successfully');
      setState(() {
        _status =
            kIsWeb
                ? 'Web payment service ready'
                : 'Mobile payment service ready';
      });
    } catch (e) {
      _addLog('Error initializing services: $e');
      setState(() {
        _status = 'Initialization failed';
      });
    }
  }

  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _logs.add('[$timestamp] $message');
    });
    print(message);
  }

  void _testDirectWebPayment() {
    if (!kIsWeb) {
      _addLog('Direct web payment test only available on web platform');
      return;
    }

    _addLog('Testing direct web payment...');
    _webService.testWebPayment();
  }

  void _testCompletePaymentFlow() async {
    setState(() {
      _isProcessing = true;
      _status = 'Testing payment flow...';
    });

    try {
      final amount = double.parse(_amountController.text);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.user;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      _addLog('Starting payment flow for ₹$amount');
      _addLog('Platform: ${kIsWeb ? "Web" : "Mobile"}');

      // Process payment (transaction ID will be generated by backend)
      final result = await _razorpayService.processWalletTopUp(
        context: context,
        amount: amount,
        currency: 'INR',
        userEmail: user.email ?? '<EMAIL>',
        userPhone: user.phone ?? '**********',
        userName: user.name ?? 'Test User',
        transactionId: null, // Let backend generate the transaction ID
      );

      _addLog('Payment completed successfully');
      _addLog('Result: $result');

      // Refresh wallet details
      final walletProvider = Provider.of<WalletProvider>(
        context,
        listen: false,
      );
      await walletProvider.fetchWalletDetails();
      _addLog('Wallet details refreshed');

      setState(() {
        _isProcessing = false;
        _status = 'Payment successful!';
      });

      _showSuccessDialog(result);
    } catch (e) {
      _addLog('Payment failed: $e');
      setState(() {
        _isProcessing = false;
        _status = 'Payment failed';
      });

      _showErrorDialog(e.toString());
    }
  }

  void _testBackendConnection() async {
    _addLog('Testing backend connection...');

    try {
      final walletProvider = Provider.of<WalletProvider>(
        context,
        listen: false,
      );
      await walletProvider.fetchWalletDetails();
      _addLog('Backend connection successful');
      _addLog('Current balance: ${walletProvider.balance}');
    } catch (e) {
      _addLog('Backend connection failed: $e');
    }
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
      _status = 'Logs cleared';
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _razorpayService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final walletProvider = Provider.of<WalletProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Payment System Test',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        backgroundColor: const Color(0xFFFFCC00),
        foregroundColor: Colors.black,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Platform Info Card
            _buildInfoCard(isDarkMode, walletProvider),
            const SizedBox(height: 16),

            // Test Controls Card
            _buildTestControlsCard(isDarkMode),
            const SizedBox(height: 16),

            // Status Card
            _buildStatusCard(isDarkMode),
            const SizedBox(height: 16),

            // Logs Card
            _buildLogsCard(isDarkMode),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(bool isDarkMode, WalletProvider walletProvider) {
    return Card(
      color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Platform Information',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Platform', kIsWeb ? 'Web Browser' : 'Mobile App'),
            _buildInfoRow(
              'Payment Service',
              kIsWeb ? 'Razorpay Web SDK' : 'Razorpay Flutter Plugin',
            ),
            _buildInfoRow(
              'Current Balance',
              '₹${walletProvider.balance?.toStringAsFixed(2) ?? "Loading..."}',
            ),
            _buildInfoRow(
              'Database Schema',
              'Fixed (payment_gateway column added)',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value, style: GoogleFonts.poppins(color: Colors.blue)),
          ),
        ],
      ),
    );
  }

  Widget _buildTestControlsCard(bool isDarkMode) {
    return Card(
      color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Controls',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 16),

            // Amount input
            TextFormField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Test Amount (₹)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixText: '₹ ',
              ),
            ),
            const SizedBox(height: 16),

            // Test buttons
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isProcessing ? null : _testCompletePaymentFlow,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFFCC00),
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(
                  _isProcessing
                      ? 'Processing...'
                      : 'Test Complete Payment Flow',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                ),
              ),
            ),
            const SizedBox(height: 8),

            if (kIsWeb) ...[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _testDirectWebPayment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text(
                    'Test Direct Web Payment',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                  ),
                ),
              ),
              const SizedBox(height: 8),
            ],

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _testBackendConnection,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(
                  'Test Backend Connection',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(bool isDarkMode) {
    return Card(
      color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Status',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _status,
              style: GoogleFonts.poppins(
                fontSize: 16,
                color:
                    _status.contains('failed') || _status.contains('error')
                        ? Colors.red
                        : _status.contains('successful')
                        ? Colors.green
                        : Colors.blue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogsCard(bool isDarkMode) {
    return Card(
      color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Test Logs',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                TextButton(onPressed: _clearLogs, child: Text('Clear')),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 200,
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.black : Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children:
                      _logs
                          .map(
                            (log) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                log,
                                style: GoogleFonts.robotoMono(
                                  fontSize: 12,
                                  color:
                                      isDarkMode
                                          ? Colors.green
                                          : Colors.black87,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Payment Successful'),
            content: Text(
              'Payment completed successfully!\n\nTransaction ID: ${result['transaction_id']}',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Payment Failed'),
            content: Text(error),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('OK'),
              ),
            ],
          ),
    );
  }
}

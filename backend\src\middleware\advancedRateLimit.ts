/**
 * Advanced rate limiting middleware with progressive backoff
 * and account-specific protection to prevent credential stuffing and brute force attacks
 */
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { getStorage } from '../config/redis';

// Store for client IPs who are being throttled
interface ThrottleStore {
  [key: string]: {
    count: number;
    resetTime: number;
    lastAttemptTime: number;
    consecutiveFailures: number;
  };
}

// In-memory fallback store for when Red<PERSON> is unavailable
const memoryStore: ThrottleStore = {};

// Cleanup old entries in memory store periodically
setInterval(() => {
  const now = Date.now();
  for (const key in memoryStore) {
    if (memoryStore[key].resetTime < now) {
      delete memoryStore[key];
    }
  }
}, 60000); // Cleanup every minute

/**
 * Helper to get and increment account-specific rate limiting
 * This helps prevent credential stuffing by tracking login attempts per account
 * @param identifier Account identifier (email, username)
 * @param maxAttempts Maximum attempts allowed
 * @param windowMs Time window in milliseconds
 * @returns Object with isLimited flag and retryAfter seconds
 */
export const checkAccountRateLimiting = async (
  identifier: string,
  maxAttempts: number = 5,
  windowMs: number = 15 * 60 * 1000
): Promise<{ isLimited: boolean; retryAfter: number }> => {
  if (!identifier) {
    return { isLimited: false, retryAfter: 0 };
  }

  try {
    const storage = getStorage();
    const key = `acct:${identifier.toLowerCase()}`;
    const now = Date.now();

    // Get current attempts count
    let data = null;
    try {
      const storedData = await storage.get(key);
      if (storedData) {
        data = JSON.parse(storedData);
      }
    } catch (e) {
      logger.debug(`Error parsing rate limit data: ${e}`);
    }

    // Initialize or reset if expired
    if (!data || data.resetTime < now) {
      data = {
        count: 1,
        resetTime: now + windowMs,
        lastAttemptTime: now,
        consecutiveFailures: 0
      };
      await storage.set(key, JSON.stringify(data));
      await storage.expire(key, Math.ceil(windowMs / 1000));
      return { isLimited: false, retryAfter: 0 };
    }

    // Increment attempt count
    data.count++;
    data.lastAttemptTime = now;

    // Store updated data with expiry
    await storage.set(key, JSON.stringify(data));
    await storage.expire(key, Math.ceil((data.resetTime - now) / 1000));

    // Check if rate limit exceeded
    if (data.count > maxAttempts) {
      const retryAfter = Math.max(0, Math.ceil((data.resetTime - now) / 1000));
      return { isLimited: true, retryAfter };
    }

    return { isLimited: false, retryAfter: 0 };
  } catch (error) {
    logger.error(`Error in account rate limiting: ${error instanceof Error ? error.message : String(error)}`);
    return { isLimited: false, retryAfter: 0 }; // Fail open for account limits
  }
};

/**
 * Record login failure for an account and optionally increase throttling
 * This implements a progressive backoff for repeated failures
 * @param identifier Account identifier (email, username)
 */
export const recordLoginFailure = async (identifier: string): Promise<void> => {
  if (!identifier) return;

  try {
    const storage = getStorage();
    const key = `acct:${identifier.toLowerCase()}`;
    const now = Date.now();
    
    // Longer window for consecutive failures (up to 24 hours)
    const baseWindowMs = 15 * 60 * 1000; // 15 minutes
    const maxWindowMs = 24 * 60 * 60 * 1000; // 24 hours
    
    // Get current data
    let data = null;
    try {
      const storedData = await storage.get(key);
      if (storedData) {
        data = JSON.parse(storedData);
      }
    } catch (e) {
      logger.debug(`Error parsing rate limit data: ${e}`);
    }
    
    if (!data) {
      data = {
        count: 1,
        resetTime: now + baseWindowMs,
        lastAttemptTime: now,
        consecutiveFailures: 1
      };
    } else {
      data.lastAttemptTime = now;
      data.consecutiveFailures = (data.consecutiveFailures || 0) + 1;
      
      // Extend reset time based on consecutive failures with exponential backoff
      // 2^n minutes (capped at 24 hours) where n is number of consecutive failures
      const backoffFactor = Math.min(10, data.consecutiveFailures);
      const extendMs = Math.min(
        maxWindowMs,
        baseWindowMs * Math.pow(2, backoffFactor - 1)
      );
      
      data.resetTime = now + extendMs;
    }
    
    // Store with new expiry
    const expireInSeconds = Math.ceil((data.resetTime - now) / 1000);
    await storage.set(key, JSON.stringify(data));
    await storage.expire(key, expireInSeconds);
    
    // Log suspicious activity if many consecutive failures
    if (data.consecutiveFailures >= 5) {
      logger.warn(`Security alert: ${data.consecutiveFailures} consecutive login failures for account: ${identifier}`);
    }
  } catch (error) {
    logger.error(`Error recording login failure: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * Reset login failure count after successful login
 * @param identifier Account identifier (email, username)
 */
export const resetLoginFailures = async (identifier: string): Promise<void> => {
  if (!identifier) return;
  
  try {
    const storage = getStorage();
    const key = `acct:${identifier.toLowerCase()}`;
    await storage.del(key);
  } catch (error) {
    logger.error(`Error resetting login failures: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * Advanced rate limit middleware for sensitive endpoints
 * Includes IP-based rate limiting with exponential backoff
 */
export const advancedRateLimit = (options: {
  windowMs?: number;
  maxAttempts?: number;
  keyGenerator?: (req: Request) => string;
}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes by default
    maxAttempts = 10, // Default max attempts
    keyGenerator = (req) => req.ip || req.socket.remoteAddress || 'unknown'
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Skip rate limiting in development mode
      if (process.env.NODE_ENV === 'development') {
        return next();
      }
      const clientKey = keyGenerator(req);
      const storage = getStorage();
      const key = `advlimit:${clientKey}`;
      const now = Date.now();
      
      // Get current data
      let data: any = null;
      try {
        const storedData = await storage.get(key);
        if (storedData) {
          data = JSON.parse(storedData);
        }
      } catch (e) {
        logger.debug(`Error parsing advanced rate limit data: ${e}`);
      }
      
      // Initialize or reset if expired
      if (!data || data.resetTime < now) {
        data = {
          count: 1,
          resetTime: now + windowMs,
          lastAttemptTime: now,
          consecutiveFailures: 0
        };
        
        await storage.set(key, JSON.stringify(data));
        await storage.expire(key, Math.ceil(windowMs / 1000));
        
        // Set rate limit headers
        res.setHeader('X-RateLimit-Limit', maxAttempts.toString());
        res.setHeader('X-RateLimit-Remaining', (maxAttempts - 1).toString());
        res.setHeader('X-RateLimit-Reset', Math.floor(data.resetTime / 1000).toString());
        
        return next();
      }
      
      // Increment count
      data.count++;
      data.lastAttemptTime = now;
      
      // Calculate dynamic delay based on request frequency
      // This adds increasing delays between requests if they come too quickly
      let dynamicDelay = 0;
      if (data.count > 3) {
        // Calculate time since last attempt
        const timeSinceLastAttempt = now - data.lastAttemptTime;
        
        // If requests are coming very quickly, add increasing delays
        if (timeSinceLastAttempt < 1000) { // Less than 1 second between requests
          const baseDelay = 1000; // 1 second base delay
          dynamicDelay = Math.min(30000, baseDelay * Math.pow(2, data.count - 4)); // Cap at 30 seconds
        }
      }
      
      // Store updated data
      await storage.set(key, JSON.stringify(data));
      await storage.expire(key, Math.ceil((data.resetTime - now) / 1000));
      
      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', maxAttempts.toString());
      res.setHeader('X-RateLimit-Remaining', Math.max(0, maxAttempts - data.count).toString());
      res.setHeader('X-RateLimit-Reset', Math.floor(data.resetTime / 1000).toString());
      
      // Check if rate limit exceeded
      if (data.count > maxAttempts) {
        // Calculate retry after time, including dynamic delay if any
        const retryAfter = Math.max(0, Math.ceil((data.resetTime - now) / 1000));
        res.setHeader('Retry-After', retryAfter.toString());
        
        logger.warn(`Advanced rate limit exceeded for ${clientKey}`);
        
        // Apply dynamic delay if needed
        if (dynamicDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, dynamicDelay));
        }
        
        return res.status(429).json({
          error: 'Too many requests, please try again later',
          retryAfter: retryAfter
        });
      }
      
      // Apply dynamic delay if needed then proceed
      if (dynamicDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, dynamicDelay));
      }
      
      return next();
    } catch (error) {
      // If rate limiting fails, allow request but log error
      logger.error(`Advanced rate limit error: ${error instanceof Error ? error.message : String(error)}`);
      return next();
    }
  };
};

// Export for use in controllers
export const sensitiveAuthEndpointLimit = advancedRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 5, // Strict limit for sensitive endpoints
});

// Special rate limit for APIs like password reset that could be abused
export const passwordResetLimit = advancedRateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxAttempts: 3, // Very strict limit
});

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/auth_service.dart';

// Simple User class for convenient access to user properties
class User {
  final String? id;
  final String? email;
  final String? phone;
  final String? name;
  final String? username;

  User({
    this.id,
    this.email,
    this.phone,
    this.name,
    this.username,
  });

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id']?.toString(),
      email: map['email']?.toString(),
      phone: map['phone']?.toString(),
      name: map['name']?.toString() ?? map['username']?.toString(),
      username: map['username']?.toString(),
    );
  }
}

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  bool _isAuthenticated = false;
  bool _isLoading = true;
  Map<String, dynamic>? _userData;
  String? _errorMessage;
  
  // Getters
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  Map<String, dynamic>? get userData => _userData;
  String? get errorMessage => _errorMessage;
  AuthService get authService => _authService; // Expose auth service for profile-related operations

  // User getter for convenient access to user properties
  User? get user => _userData != null ? User.fromMap(_userData!) : null;

  // Token getter for API calls
  Future<String?> get token async => await _authService.getToken();
  
  // Constructor initializes auth state
  AuthProvider() {
    _initAuthState();
  }
  
  // Initialize authentication state
  Future<void> _initAuthState() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      // Try to verify authentication with a short timeout
      final isAuth = await _authService.isAuthenticated();
      debugPrint('Authentication initialized. Status: $isAuth');
      
      if (isAuth) {
        _userData = await _authService.getUserData();
        _isAuthenticated = true;
      } else {
        _isAuthenticated = false;
        _userData = null;
      }
    } catch (e) {
      debugPrint('Error initializing auth state: $e');
      _isAuthenticated = false;
      _userData = null;
    } finally {
      // Always ensure loading state is set to false
      _isLoading = false;
      notifyListeners();
      debugPrint('Auth state initialized. IsLoading: $_isLoading, IsAuthenticated: $_isAuthenticated');
    }
  }
  
  // Login method
  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      final error = await _authService.login(email, password);
      
      if (error == null) {
        // Login successful
        _userData = await _authService.getUserData();
        _isAuthenticated = true;
        _errorMessage = null;
        notifyListeners();
        return true;
      } else {
        // Login failed
        _errorMessage = error;
        _isAuthenticated = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'An unexpected error occurred: $e';
      _isAuthenticated = false;
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Register method
  Future<bool> register({
    required String username,
    required String email,
    required String password,
    String? phone,
    String? country,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
    
    try {
      final error = await _authService.register(
        username: username,
        email: email,
        password: password,
        phone: phone,
        country: country,
      );
      
      if (error == null) {
        // Registration successful
        _userData = await _authService.getUserData();
        _isAuthenticated = true;
        _errorMessage = null;
        notifyListeners();
        return true;
      } else {
        // Registration failed
        _errorMessage = error;
        _isAuthenticated = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = 'An unexpected error occurred: $e';
      _isAuthenticated = false;
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Logout method
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      await _authService.logout();
      _isAuthenticated = false;
      _userData = null;
    } catch (e) {
      debugPrint('Error during logout: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Refresh authentication
  Future<void> refreshAuthentication() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      final isAuth = await _authService.isAuthenticated();
      debugPrint('Authentication refreshed. Status: $isAuth');
      
      if (isAuth) {
        _userData = await _authService.getUserData();
        _isAuthenticated = true;
      } else {
        _isAuthenticated = false;
        _userData = null;
      }
    } catch (e) {
      debugPrint('Error refreshing authentication: $e');
      _isAuthenticated = false;
      _userData = null;
    } finally {
      // Always ensure loading state is set to false
      _isLoading = false;
      notifyListeners();
      debugPrint('Auth refresh completed. IsLoading: $_isLoading, IsAuthenticated: $_isAuthenticated');
    }
  }
  
  // Clear any error messages
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}

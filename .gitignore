# Project-specific exclusions
/final_plan/
/gaming-betting-app/
/supabase/
/updated_plan/
.windsurfrules
auth_prompts.md
windsurfrules.md
start-backend.bat
wallet.md
rewards.md
package-lock.json
package.json
rewards_implementation_plan.md
rewards_tasks.json
taskmanager.md
tasks.js
tasks.json
verify-rewards-schema.js
/node_modules/
auth_plan.md
user_mgmt.md
/test-plans/

# Node.js Backend
/backend/node_modules/
/backend/.env
/backend/dist/
/backend/coverage/
/backend/npm-debug.log
/backend/yarn-debug.log
/backend/yarn-error.log
/backend/.pnpm-debug.log

# Flutter/Dart Frontend
/frontend/wiggyz/.dart_tool/
/frontend/wiggyz/.flutter-plugins
/frontend/wiggyz/.flutter-plugins-dependencies
/frontend/wiggyz/.packages
/frontend/wiggyz/build/
/frontend/wiggyz/.pub/
/frontend/wiggyz/pubspec.lock
/frontend/wiggyz/.pub-cache/
/frontend/wiggyz/.idea/
/frontend/wiggyz/.vscode/
/frontend/wiggyz/*.iml
/frontend/wiggyz/*.ipr
/frontend/wiggyz/*.iws
/frontend/wiggyz/.DS_Store

# Android related
/frontend/wiggyz/android/gradle-wrapper.jar
/frontend/wiggyz/android/.gradle
/frontend/wiggyz/android/captures/
/frontend/wiggyz/android/gradlew
/frontend/wiggyz/android/gradlew.bat
/frontend/wiggyz/android/local.properties
/frontend/wiggyz/android/**/GeneratedPluginRegistrant.java
/frontend/wiggyz/android/key.properties
/frontend/wiggyz/android/app/debug
/frontend/wiggyz/android/app/profile
/frontend/wiggyz/android/app/release
# Java heap dumps and profiling files
*.hprof
/frontend/android/*.hprof

# iOS/XCode related
/frontend/wiggyz/ios/**/*.mode1v3
/frontend/wiggyz/ios/**/*.mode2v3
/frontend/wiggyz/ios/**/*.moved-aside
/frontend/wiggyz/ios/**/*.pbxuser
/frontend/wiggyz/ios/**/*.perspectivev3
/frontend/wiggyz/ios/**/*sync/
/frontend/wiggyz/ios/**/.sconsign.dblite
/frontend/wiggyz/ios/**/.tags*
/frontend/wiggyz/ios/**/.vagrant/
/frontend/wiggyz/ios/**/DerivedData/
/frontend/wiggyz/ios/**/Icon?
/frontend/wiggyz/ios/**/Pods/
/frontend/wiggyz/ios/**/.symlinks/
/frontend/wiggyz/ios/**/profile
/frontend/wiggyz/ios/**/xcuserdata
/frontend/wiggyz/ios/.generated/
/frontend/wiggyz/ios/Flutter/App.framework
/frontend/wiggyz/ios/Flutter/Flutter.framework
/frontend/wiggyz/ios/Flutter/Flutter.podspec
/frontend/wiggyz/ios/Flutter/Generated.xcconfig
/frontend/wiggyz/ios/Flutter/ephemeral/
/frontend/wiggyz/ios/Flutter/app.flx
/frontend/wiggyz/ios/Flutter/app.zip
/frontend/wiggyz/ios/Flutter/flutter_assets/
/frontend/wiggyz/ios/Flutter/flutter_export_environment.sh
/frontend/wiggyz/ios/ServiceDefinitions.json
/frontend/wiggyz/ios/Runner/GeneratedPluginRegistrant.*

# macOS
/frontend/wiggyz/macos/Flutter/GeneratedPluginRegistrant.swift
/frontend/wiggyz/macos/Flutter/ephemeral/

# VS Code
.vscode/

# IntelliJ
.idea/

# General
.DS_Store
*.log
*.swp
.history

# WiggyZ Backend - Quick Deployment Steps

## 🚨 Problem Solved
Vercel deployments are protected by organization-level authentication that cannot be disabled. Solution: Deploy to Render.com for public API access.

## ⚡ Quick Deployment (15 minutes)

### Step 1: Deploy to Render.com

1. **Go to Render.com**
   - Visit https://render.com
   - Sign up/login and connect your GitHub account

2. **Create Web Service**
   - Click "New +" → "Web Service"
   - Select your WiggyZ repository
   - Configure:
     ```
     Name: wiggyz-backend
     Environment: Node
     Root Directory: backend
     Build Command: npm install && npm run build
     Start Command: npm start
     ```

3. **Add Environment Variables**
   ```
   NODE_ENV=production
   PORT=10000
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   JWT_REFRESH_SECRET=your_jwt_secret
   RAZORPAY_KEY_ID=your_razorpay_key_id
   RAZORPAY_KEY_SECRET=your_razorpay_key_secret
   ```

4. **Deploy**
   - Click "Create Web Service"
   - Wait for deployment (5-10 minutes)
   - Note your URL: `https://your-app-name.onrender.com`

### Step 2: Test Deployment

```bash
# Test health endpoint
curl https://your-app-name.onrender.com/health

# Should return: {"status":"ok"}
```

### Step 3: Update Flutter App

Edit `frontend/lib/core/api/api_config.dart`:

```dart
static String get baseUrl {
  // Replace with your actual Render URL
  return 'https://your-app-name.onrender.com/api/v1';
}
```

### Step 4: Test Mobile App

1. Build and run Flutter app
2. Test login/registration
3. Verify API connectivity

## 🧪 Validation Script

```bash
# Validate configuration
node scripts/deployment/deploy-render.js

# Test deployed endpoint
node scripts/deployment/deploy-render.js https://your-app-name.onrender.com
```

## 📋 Environment Variables Checklist

Make sure you have these values ready:

- [ ] `SUPABASE_URL` - Your Supabase project URL
- [ ] `SUPABASE_KEY` - Your Supabase anon key
- [ ] `JWT_REFRESH_SECRET` - JWT secret for token refresh
- [ ] `RAZORPAY_KEY_ID` - Razorpay API key ID
- [ ] `RAZORPAY_KEY_SECRET` - Razorpay API secret

## 🔧 Files Ready for Deployment

All necessary files have been created:

- ✅ `backend/render.yaml` - Render configuration
- ✅ `docs/RENDER_DEPLOYMENT.md` - Detailed guide
- ✅ `scripts/deployment/deploy-render.js` - Helper script
- ✅ `frontend/lib/core/api/api_config.dart` - Updated with TODO

## 🚀 Expected Result

After deployment, you'll have:

- **Public API URL**: `https://your-app-name.onrender.com/api/v1`
- **No authentication barriers**: Direct API access
- **Always-on hosting**: No cold starts (on paid plans)
- **Mobile app connectivity**: Full functionality restored

## 💡 Pro Tips

1. **Free Tier Sleep**: Apps sleep after 15 minutes. Use UptimeRobot to keep warm.
2. **Custom Domain**: Render supports custom domains on paid plans.
3. **Monitoring**: Use Render's built-in metrics and logs.
4. **Scaling**: Easy to upgrade to paid plans for better performance.

## 🆘 If Issues Occur

1. **Check Render Logs**: Dashboard → Your Service → Logs
2. **Validate Config**: Run `node scripts/deployment/deploy-render.js`
3. **Test Endpoints**: Use curl or Postman to test API
4. **Environment Variables**: Double-check all values in Render dashboard

## 📞 Support Resources

- **Render Documentation**: https://render.com/docs
- **WiggyZ Deployment Guide**: `docs/RENDER_DEPLOYMENT.md`
- **Configuration Helper**: `scripts/deployment/deploy-render.js`

---

**Next Action**: Deploy to Render.com using the steps above!

// test-backend-session-isolation.js
// Comprehensive test to verify backend session isolation fixes
// Tests that multiple users can remain logged in without API failures

const axios = require('axios');
require('dotenv').config({ path: './backend/.env.local' });

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001/api/v1';
const TEST_TIMEOUT = 30000; // 30 seconds

// Test users for session isolation testing
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    username: 'testuser1'
  },
  {
    email: '<EMAIL>', 
    password: 'TestPassword123!',
    username: 'testuser2'
  },
  {
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    username: 'admin'
  }
];

// Store user sessions
const userSessions = [];

async function loginUser(userCredentials) {
  try {
    console.log(`🔐 Logging in user: ${userCredentials.email}`);
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: userCredentials.email,
      password: userCredentials.password
    }, {
      timeout: TEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 200 && response.data.data?.accessToken) {
      console.log(`✅ Login successful for ${userCredentials.email}`);
      return {
        user: userCredentials,
        token: response.data.data.accessToken,
        userId: response.data.data.user?.id,
        loginTime: Date.now()
      };
    } else {
      console.error(`❌ Login failed for ${userCredentials.email}: Invalid response`);
      return null;
    }
  } catch (error) {
    console.error(`❌ Login failed for ${userCredentials.email}:`, error.response?.data || error.message);
    return null;
  }
}

async function testWalletAPI(session) {
  try {
    console.log(`💰 Testing wallet API for ${session.user.email}`);
    
    const response = await axios.get(`${API_BASE_URL}/wallet`, {
      headers: {
        'Authorization': `Bearer ${session.token}`,
        'Content-Type': 'application/json'
      },
      timeout: TEST_TIMEOUT
    });

    if (response.status === 200) {
      console.log(`✅ Wallet API success for ${session.user.email}: Balance ${response.data.data?.balance || 0}`);
      return { success: true, data: response.data };
    } else {
      console.error(`❌ Wallet API failed for ${session.user.email}: Status ${response.status}`);
      return { success: false, error: `Status ${response.status}` };
    }
  } catch (error) {
    console.error(`❌ Wallet API error for ${session.user.email}:`, error.response?.status, error.response?.data || error.message);
    return { success: false, error: error.response?.status || error.message };
  }
}

async function testMatchHistoryAPI(session) {
  try {
    console.log(`🎮 Testing match history API for ${session.user.email}`);
    
    const response = await axios.get(`${API_BASE_URL}/matches/user/joined-matches?limit=5`, {
      headers: {
        'Authorization': `Bearer ${session.token}`,
        'Content-Type': 'application/json'
      },
      timeout: TEST_TIMEOUT
    });

    if (response.status === 200) {
      console.log(`✅ Match history API success for ${session.user.email}: ${response.data.data?.matches?.length || 0} matches`);
      return { success: true, data: response.data };
    } else {
      console.error(`❌ Match history API failed for ${session.user.email}: Status ${response.status}`);
      return { success: false, error: `Status ${response.status}` };
    }
  } catch (error) {
    console.error(`❌ Match history API error for ${session.user.email}:`, error.response?.status, error.response?.data || error.message);
    return { success: false, error: error.response?.status || error.message };
  }
}

async function testProfileAPI(session) {
  try {
    console.log(`👤 Testing profile API for ${session.user.email}`);
    
    const response = await axios.get(`${API_BASE_URL}/profile`, {
      headers: {
        'Authorization': `Bearer ${session.token}`,
        'Content-Type': 'application/json'
      },
      timeout: TEST_TIMEOUT
    });

    if (response.status === 200) {
      console.log(`✅ Profile API success for ${session.user.email}: User ${response.data.data?.username || 'unknown'}`);
      return { success: true, data: response.data };
    } else {
      console.error(`❌ Profile API failed for ${session.user.email}: Status ${response.status}`);
      return { success: false, error: `Status ${response.status}` };
    }
  } catch (error) {
    console.error(`❌ Profile API error for ${session.user.email}:`, error.response?.status, error.response?.data || error.message);
    return { success: false, error: error.response?.status || error.message };
  }
}

async function runConcurrentAPITests(sessions) {
  console.log('\n🔄 Running concurrent API tests for all users...');
  
  const testPromises = [];
  
  // Test all APIs for all users concurrently
  for (const session of sessions) {
    testPromises.push(
      Promise.all([
        testWalletAPI(session),
        testMatchHistoryAPI(session),
        testProfileAPI(session)
      ]).then(results => ({
        user: session.user.email,
        wallet: results[0],
        matchHistory: results[1],
        profile: results[2]
      }))
    );
  }
  
  const results = await Promise.all(testPromises);
  
  // Analyze results
  let allTestsPassed = true;
  const failedTests = [];
  
  for (const userResult of results) {
    console.log(`\n📊 Results for ${userResult.user}:`);
    
    if (!userResult.wallet.success) {
      console.log(`  ❌ Wallet API: ${userResult.wallet.error}`);
      allTestsPassed = false;
      failedTests.push(`${userResult.user} - Wallet API`);
    } else {
      console.log(`  ✅ Wallet API: Success`);
    }
    
    if (!userResult.matchHistory.success) {
      console.log(`  ❌ Match History API: ${userResult.matchHistory.error}`);
      allTestsPassed = false;
      failedTests.push(`${userResult.user} - Match History API`);
    } else {
      console.log(`  ✅ Match History API: Success`);
    }
    
    if (!userResult.profile.success) {
      console.log(`  ❌ Profile API: ${userResult.profile.error}`);
      allTestsPassed = false;
      failedTests.push(`${userResult.user} - Profile API`);
    } else {
      console.log(`  ✅ Profile API: Success`);
    }
  }
  
  return { allTestsPassed, failedTests, results };
}

async function main() {
  console.log('🧪 Backend Session Isolation Test');
  console.log('==================================');
  console.log(`Testing against: ${API_BASE_URL}`);
  
  try {
    // Step 1: Login all users
    console.log('\n📝 Step 1: Logging in all test users...');
    
    for (const user of testUsers) {
      const session = await loginUser(user);
      if (session) {
        userSessions.push(session);
      }
    }
    
    if (userSessions.length === 0) {
      console.error('❌ No users could be logged in. Check credentials and backend status.');
      return;
    }
    
    console.log(`✅ Successfully logged in ${userSessions.length}/${testUsers.length} users`);
    
    // Step 2: Test concurrent API access
    console.log('\n📝 Step 2: Testing concurrent API access...');
    
    const testResults = await runConcurrentAPITests(userSessions);
    
    // Step 3: Test session isolation after new login
    console.log('\n📝 Step 3: Testing session isolation after new user login...');
    
    if (userSessions.length >= 2) {
      // Login the first user again to test if it affects other sessions
      console.log(`🔄 Re-logging in ${userSessions[0].user.email}...`);
      const newSession = await loginUser(userSessions[0].user);
      
      if (newSession) {
        userSessions[0] = newSession; // Update the session
        
        // Test if other users' sessions are still working
        console.log('🧪 Testing if other users\' sessions are still active...');
        const otherSessions = userSessions.slice(1);
        const isolationTestResults = await runConcurrentAPITests(otherSessions);
        
        if (isolationTestResults.allTestsPassed) {
          console.log('✅ Session isolation test passed: Other users\' sessions remain active');
        } else {
          console.log('❌ Session isolation test failed: Other users\' sessions were affected');
          console.log('Failed tests:', isolationTestResults.failedTests);
        }
      }
    }
    
    // Final Results
    console.log('\n📊 Final Test Results');
    console.log('=====================');
    
    if (testResults.allTestsPassed) {
      console.log('🎉 SUCCESS: All backend session isolation tests passed!');
      console.log('✅ Multiple users can remain logged in simultaneously');
      console.log('✅ API endpoints work correctly for all users');
      console.log('✅ No cross-session interference detected');
    } else {
      console.log('❌ FAILURE: Some backend session isolation tests failed');
      console.log('Failed tests:');
      testResults.failedTests.forEach(test => console.log(`  - ${test}`));
      console.log('\nThis indicates partial session isolation issues in the backend.');
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, loginUser, testWalletAPI, testMatchHistoryAPI, testProfileAPI };

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { 
  MessageCircle, 
  Clock, 
  User, 
  Send,
  Eye,
  RefreshCw
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface ChatSession {
  id: string
  user_id: string
  admin_user_id?: string
  session_type: string
  status: string
  started_at: string
  ended_at?: string
  last_activity: string
  user?: {
    id: string
    name: string
    email: string
  }
}

interface ChatMessage {
  id: string
  chat_session_id: string
  sender_id?: string
  sender_type: string
  message_text: string
  message_type: string
  is_read: boolean
  read_at?: string
  created_at: string
  sender?: {
    id: string
    name: string
    email: string
  }
}

export default function ChatManagementPage() {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([])
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [replyText, setReplyText] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingMessages, setIsLoadingMessages] = useState(false)
  const [isSendingMessage, setIsSendingMessage] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchChatSessions()
  }, [])

  const fetchChatSessions = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/support/chat/sessions')
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch chat sessions')
      }

      const data = await response.json()
      setChatSessions(data.data || [])
    } catch (error) {
      console.error('Error fetching chat sessions:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch chat sessions",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchMessages = async (sessionId: string) => {
    try {
      setIsLoadingMessages(true)
      const response = await fetch(`/api/support/chat/sessions/${sessionId}/messages`)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch messages')
      }

      const data = await response.json()
      setMessages(data.data || [])
    } catch (error) {
      console.error('Error fetching messages:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch messages",
        variant: "destructive"
      })
    } finally {
      setIsLoadingMessages(false)
    }
  }

  const sendMessage = async () => {
    if (!selectedSession || !replyText.trim()) return

    try {
      setIsSendingMessage(true)

      const response = await fetch(`/api/support/chat/sessions/${selectedSession.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message_text: replyText,
          message_type: 'text',
          metadata: {}
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to send message')
      }

      setReplyText("")
      await fetchMessages(selectedSession.id)
      await fetchChatSessions() // Refresh sessions to update last activity

      toast({
        title: "Success",
        description: "Message sent successfully"
      })
    } catch (error) {
      console.error('Error sending message:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send message",
        variant: "destructive"
      })
    } finally {
      setIsSendingMessage(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: 'Active', variant: 'default' as const },
      waiting: { label: 'Waiting', variant: 'secondary' as const },
      closed: { label: 'Closed', variant: 'outline' as const },
      transferred: { label: 'Transferred', variant: 'destructive' as const }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Chat Management</h1>
          <p className="text-muted-foreground">Manage real-time chat sessions with users</p>
        </div>
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Chat Management</h1>
          <p className="text-muted-foreground">Manage real-time chat sessions with users</p>
        </div>
        <Button onClick={fetchChatSessions} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {chatSessions.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center p-12">
            <div className="text-center">
              <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Chat Sessions</h3>
              <p className="text-muted-foreground">No active chat sessions found</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {chatSessions.map((session) => (
            <Card key={session.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">Chat Session</h3>
                      {getStatusBadge(session.status)}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {session.user?.name || session.user?.email || 'Unknown User'}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        Started {formatDistanceToNow(new Date(session.started_at), { addSuffix: true })}
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="h-4 w-4" />
                        Last activity {formatDistanceToNow(new Date(session.last_activity), { addSuffix: true })}
                      </div>
                      <div className="text-xs bg-muted px-2 py-1 rounded">
                        Session ID: {session.id.slice(0, 8)}...
                      </div>
                    </div>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => {
                          setSelectedSession(session)
                          fetchMessages(session.id)
                        }}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Chat
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                          <MessageCircle className="h-5 w-5" />
                          Chat with {session.user?.name || 'Unknown User'}
                        </DialogTitle>
                        <DialogDescription>
                          Session started {formatDistanceToNow(new Date(session.started_at), { addSuffix: true })}
                        </DialogDescription>
                      </DialogHeader>
                      
                      <div className="space-y-4">
                        {/* Messages */}
                        <div className="border rounded-lg p-4 max-h-96 overflow-y-auto space-y-3">
                          {isLoadingMessages ? (
                            <div className="space-y-2">
                              {[...Array(3)].map((_, i) => (
                                <Skeleton key={i} className="h-12 w-full" />
                              ))}
                            </div>
                          ) : messages.length === 0 ? (
                            <p className="text-center text-muted-foreground">No messages yet</p>
                          ) : (
                            messages.map((message) => (
                              <div 
                                key={message.id} 
                                className={`flex ${message.sender_type === 'admin' ? 'justify-end' : 'justify-start'}`}
                              >
                                <div 
                                  className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                                    message.sender_type === 'admin' 
                                      ? 'bg-primary text-primary-foreground' 
                                      : 'bg-muted'
                                  }`}
                                >
                                  <p className="text-sm">{message.message_text}</p>
                                  <p className="text-xs opacity-70 mt-1">
                                    {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                                  </p>
                                </div>
                              </div>
                            ))
                          )}
                        </div>

                        {/* Reply Form */}
                        <div className="space-y-3">
                          <Label>Send Message</Label>
                          <Textarea
                            placeholder="Type your message..."
                            value={replyText}
                            onChange={(e) => setReplyText(e.target.value)}
                            rows={3}
                          />
                          <div className="flex justify-end">
                            <Button 
                              onClick={sendMessage}
                              disabled={!replyText.trim() || isSendingMessage}
                            >
                              <Send className="h-4 w-4 mr-2" />
                              {isSendingMessage ? "Sending..." : "Send Message"}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

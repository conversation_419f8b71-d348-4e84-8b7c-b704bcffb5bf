import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'

/**
 * Helper function to get admin token from Supabase session
 * This function extracts the authentication token from the admin user's session
 * to make authenticated requests to the backend API
 */
export async function getAdminToken(request: NextRequest): Promise<string> {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set() {
          // Not needed for server-side operations
        },
        remove() {
          // Not needed for server-side operations
        },
      },
    }
  )

  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error || !session?.access_token) {
    throw new Error('No valid authentication session')
  }

  return `Bearer ${session.access_token}`
}

/**
 * Helper function to get the current admin user from Supabase session
 */
export async function getAdminUser(request: NextRequest) {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set() {
          // Not needed for server-side operations
        },
        remove() {
          // Not needed for server-side operations
        },
      },
    }
  )

  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error || !session) {
    throw new Error('No valid authentication session')
  }

  return session.user
}

/**
 * Helper function to check if the current user has admin privileges
 */
export async function isAdminUser(request: NextRequest): Promise<boolean> {
  try {
    const user = await getAdminUser(request)
    
    // You can add additional role checks here if needed
    // For now, we assume if they have a valid session and passed middleware, they're admin
    return !!user
  } catch {
    return false
  }
}

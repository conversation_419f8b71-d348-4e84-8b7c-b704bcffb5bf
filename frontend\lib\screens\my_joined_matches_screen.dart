import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:wiggyz_app/providers/user_joined_provider.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:wiggyz_app/features/tournament_models.dart';
import 'package:wiggyz_app/utils/match_utils.dart';

class MyJoinedMatchesScreen extends StatefulWidget {
  const MyJoinedMatchesScreen({super.key});

  @override
  State<MyJoinedMatchesScreen> createState() => _MyJoinedMatchesScreenState();
}

class _MyJoinedMatchesScreenState extends State<MyJoinedMatchesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all'; // 'all', 'active', 'upcoming', 'live'

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final userJoinedProvider = Provider.of<UserJoinedProvider>(
        context,
        listen: false,
      );
      userJoinedProvider.fetchAllJoinedItems(
        userId: authProvider.userData?['id'],
      );
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        title: Text(
          'My Matches & Tournaments',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.black),
            onPressed: () => _refreshData(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Tabs
          _buildFilterTabs(isDarkMode),

          // Tab Bar
          Container(
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.grey[100],
              border: Border(
                bottom: BorderSide(
                  color:
                      isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300,
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              indicatorColor: const Color(0xFFFFCC00),
              labelColor: isDarkMode ? Colors.white : Colors.black,
              unselectedLabelColor:
                  isDarkMode ? Colors.grey[400] : Colors.grey[600],
              tabs: const [Tab(text: 'Matches'), Tab(text: 'Tournaments')],
            ),
          ),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildMatchesTab(isDarkMode),
                _buildTournamentsTab(isDarkMode),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs(bool isDarkMode) {
    final filters = [
      {'id': 'all', 'label': 'All'},
      {'id': 'active', 'label': 'Active'},
      {'id': 'upcoming', 'label': 'Upcoming'},
      {'id': 'live', 'label': 'Live'},
    ];

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.grey[50],
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['id'];

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(filter['label']!),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter['id']!;
                });
              },
              selectedColor: const Color(0xFFFFCC00),
              backgroundColor: isDarkMode ? Colors.grey[800] : Colors.white,
              labelStyle: TextStyle(
                color:
                    isSelected
                        ? Colors.black
                        : isDarkMode
                        ? Colors.white
                        : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMatchesTab(bool isDarkMode) {
    return Consumer<UserJoinedProvider>(
      builder: (context, userJoinedProvider, child) {
        if (userJoinedProvider.isLoadingJoinedMatches) {
          return const Center(child: CircularProgressIndicator());
        }
        if (userJoinedProvider.joinedMatchesError != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.wifi_off,
                  size: 64,
                  color: isDarkMode ? Colors.red[300] : Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Connection Issue',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Unable to load your matches. Please check your connection and try again.',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _refreshData,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFCC00),
                    foregroundColor: Colors.black,
                  ),
                ),
              ],
            ),
          );
        }

        final filteredMatches = _filterMatches(
          userJoinedProvider.joinedMatches,
        );

        if (filteredMatches.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.sports_esports,
                  size: 64,
                  color: isDarkMode ? Colors.white30 : Colors.black26,
                ),
                const SizedBox(height: 16),
                Text(
                  'No matches found',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Join some matches to see them here',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white54 : Colors.black38,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredMatches.length,
          itemBuilder: (context, index) {
            return _buildMatchCard(filteredMatches[index], isDarkMode);
          },
        );
      },
    );
  }

  Widget _buildTournamentsTab(bool isDarkMode) {
    return Consumer<UserJoinedProvider>(
      builder: (context, userJoinedProvider, child) {
        if (userJoinedProvider.isLoadingJoinedTournaments) {
          return const Center(child: CircularProgressIndicator());
        }
        if (userJoinedProvider.joinedTournamentsError != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.wifi_off,
                  size: 64,
                  color: isDarkMode ? Colors.red[300] : Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Connection Issue',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Unable to load your tournaments. Please check your connection and try again.',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _refreshData,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFCC00),
                    foregroundColor: Colors.black,
                  ),
                ),
              ],
            ),
          );
        }

        final filteredTournaments = _filterTournaments(
          userJoinedProvider.joinedTournaments,
        );

        if (filteredTournaments.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.emoji_events,
                  size: 64,
                  color: isDarkMode ? Colors.white30 : Colors.black26,
                ),
                const SizedBox(height: 16),
                Text(
                  'No tournaments found',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Join some tournaments to see them here',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white54 : Colors.black38,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredTournaments.length,
          itemBuilder: (context, index) {
            return _buildTournamentCard(filteredTournaments[index], isDarkMode);
          },
        );
      },
    );
  }

  Widget _buildMatchCard(JoinedMatch joinedMatch, bool isDarkMode) {
    final match = joinedMatch.matchDetails;
    final statusColor = _getStatusColor(joinedMatch.userMatchStatus);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
      child: InkWell(
        onTap: () => _navigateToMatchDetails(joinedMatch),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Game icon/image
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFCC00),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.sports_esports,
                      color: Colors.black,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          MatchUtils.generateMatchTitle(
                            match.title,
                            joinedMatch.game?.name,
                            match.id,
                          ),
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        Text(
                          '${match.gameFormat?.toUpperCase()} • ${match.matchType?.toUpperCase()}',
                          style: TextStyle(
                            fontSize: 12,
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      joinedMatch.userMatchStatus.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Match details
              if (match.matchTime != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      MatchUtils.formatLocalDateTime(match.matchTime),
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
              if (match.entryFee != null && match.entryFee! > 0) ...[
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Entry Fee: ${MatchUtils.formatCurrency(match.entryFee!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Prize pool section
              Row(
                children: [
                  Icon(
                    Icons.emoji_events,
                    size: 16,
                    color: Colors.amber,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'Prize: ${MatchUtils.formatCurrentAndMaxPrizePool(
                        match.entryFee ?? 0.0,
                        MatchUtils.getParticipantCount(match.match_participants),
                        match.maxParticipants ?? 10, // Use reasonable default instead of 0
                      )}',
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Action button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _handleMatchAction(joinedMatch),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _getActionButtonColor(
                      joinedMatch.userMatchStatus,
                    ),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    _getActionButtonText(joinedMatch.userMatchStatus),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTournamentCard(
    JoinedTournament joinedTournament,
    bool isDarkMode,
  ) {
    final tournament = joinedTournament.tournamentDetails;
    final statusColor = _getStatusColor(joinedTournament.userTournamentStatus);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
      child: InkWell(
        onTap: () => _navigateToTournamentDetails(joinedTournament),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFCC00),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.emoji_events,
                      color: Colors.black,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tournament.name,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        if (joinedTournament.game != null)
                          Text(
                            joinedTournament.game!.name,
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  isDarkMode ? Colors.white70 : Colors.black54,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      joinedTournament.userTournamentStatus.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Tournament stats
              Row(
                children: [
                  if (joinedTournament.currentRank != null) ...[
                    Icon(
                      Icons.leaderboard,
                      size: 16,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Rank: #${joinedTournament.currentRank}',
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  Text(
                    'Matches: ${joinedTournament.matchesWon}/${joinedTournament.matchesPlayed}',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                  ),
                ],
              ),

              if (joinedTournament.hasClaimableReward) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Reward Available!',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'joined':
      case 'registered':
        return Colors.blue;
      case 'active':
      case 'live':
        return Colors.green;
      case 'pending_verification':
        return Colors.orange;
      case 'completed':
        return Colors.grey;
      case 'abandoned':
      case 'eliminated':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getActionButtonColor(String status) {
    switch (status.toLowerCase()) {
      case 'joined':
        return const Color(0xFFFFCC00);
      case 'active':
        return Colors.green;
      case 'pending_verification':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getActionButtonText(String status) {
    switch (status.toLowerCase()) {
      case 'joined':
        return 'Start Match';
      case 'active':
        return 'Submit Results';
      case 'pending_verification':
        return 'Awaiting Verification';
      case 'completed':
        return 'View Results';
      default:
        return 'View Details';
    }
  }

  List<JoinedMatch> _filterMatches(List<JoinedMatch> matches) {
    switch (_selectedFilter) {
      case 'active':
        return matches
            .where(
              (m) =>
                  m.userMatchStatus == 'active' ||
                  m.userMatchStatus == 'pending_verification',
            )
            .toList();
      case 'upcoming':
        return matches.where((m) => m.userMatchStatus == 'joined').toList();
      case 'live':
        return matches.where((m) => m.userMatchStatus == 'active').toList();
      default:
        return matches;
    }
  }

  List<JoinedTournament> _filterTournaments(
    List<JoinedTournament> tournaments,
  ) {
    switch (_selectedFilter) {
      case 'active':
        return tournaments
            .where(
              (t) =>
                  t.userTournamentStatus == 'active' ||
                  t.userTournamentStatus == 'registered',
            )
            .toList();
      case 'upcoming':
        return tournaments
            .where((t) => t.userTournamentStatus == 'registered')
            .toList();
      case 'live':
        return tournaments
            .where((t) => t.userTournamentStatus == 'active')
            .toList();
      default:
        return tournaments;
    }
  }

  void _handleMatchAction(JoinedMatch joinedMatch) {
    switch (joinedMatch.userMatchStatus.toLowerCase()) {
      case 'joined':
        _startMatch(joinedMatch);
        break;
      case 'active':
        _submitResults(joinedMatch);
        break;
      default:
        _navigateToMatchDetails(joinedMatch);
    }
  }

  void _startMatch(JoinedMatch joinedMatch) {
    // Navigate to match start screen or show start dialog
    context.push('/matches/${joinedMatch.matchId}/start');
  }

  void _submitResults(JoinedMatch joinedMatch) {
    // Navigate to result submission screen
    context.push('/matches/${joinedMatch.matchId}/submit-result');
  }

  void _navigateToMatchDetails(JoinedMatch joinedMatch) {
    context.push('/matches/${joinedMatch.matchId}');
  }

  void _navigateToTournamentDetails(JoinedTournament joinedTournament) {
    context.push('/tournaments/${joinedTournament.tournamentId}');
  }

  void _refreshData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userJoinedProvider = Provider.of<UserJoinedProvider>(
      context,
      listen: false,
    );
    userJoinedProvider.refresh(userId: authProvider.userData?['id']);
  }
}

/**
 * Test script to verify Render deployment is working correctly
 * Run this after deploying to Render to ensure all endpoints are accessible
 */

const https = require('https');
const http = require('http');

const RENDER_URL = 'https://wiggyz-backend.onrender.com';

// Test configuration
const tests = [
  {
    name: 'Health Check',
    path: '/health',
    expectedStatus: 200,
    expectedContent: 'status'
  },
  {
    name: 'API Base',
    path: '/api/v1/',
    expectedStatus: 200,
    expectedContent: 'api'
  },
  {
    name: 'Auth Endpoint (should require auth)',
    path: '/api/v1/auth/me',
    expectedStatus: 401,
    expectedContent: 'unauthorized'
  }
];

/**
 * Make HTTP request and return promise
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    // Set timeout
    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * Run a single test
 */
async function runTest(test) {
  const url = `${RENDER_URL}${test.path}`;
  
  console.log(`\n🧪 Testing: ${test.name}`);
  console.log(`   URL: ${url}`);
  
  try {
    const response = await makeRequest(url);
    
    // Check status code
    const statusMatch = response.statusCode === test.expectedStatus;
    const statusIcon = statusMatch ? '✅' : '❌';
    console.log(`   ${statusIcon} Status: ${response.statusCode} (expected: ${test.expectedStatus})`);
    
    // Check content
    const contentMatch = response.data.toLowerCase().includes(test.expectedContent.toLowerCase());
    const contentIcon = contentMatch ? '✅' : '❌';
    console.log(`   ${contentIcon} Content: Contains "${test.expectedContent}"`);
    
    // Show response preview
    const preview = response.data.substring(0, 100);
    console.log(`   📄 Response: ${preview}${response.data.length > 100 ? '...' : ''}`);
    
    return statusMatch && contentMatch;
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Testing Render Deployment');
  console.log('=' .repeat(50));
  console.log(`Base URL: ${RENDER_URL}`);
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    const passed = await runTest(test);
    if (passed) passedTests++;
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log(`📊 Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Deployment is working correctly.');
    console.log('\n✅ Next steps:');
    console.log('   1. Test Flutter app connectivity');
    console.log('   2. Test admin panel connectivity');
    console.log('   3. Verify authentication flow');
    console.log('   4. Test match creation and management');
  } else {
    console.log('⚠️  Some tests failed. Please check the deployment.');
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check Render service logs');
    console.log('   2. Verify environment variables');
    console.log('   3. Ensure build completed successfully');
    console.log('   4. Check health check endpoint');
  }
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

// Additional utility functions
function checkServiceStatus() {
  console.log('\n🔍 Checking service status...');
  console.log('Visit the Render dashboard to monitor:');
  console.log('- Build logs');
  console.log('- Runtime logs');
  console.log('- Service metrics');
  console.log('- Environment variables');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  runTest,
  makeRequest,
  RENDER_URL
};

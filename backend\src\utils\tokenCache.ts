/**
 * Distributed token cache service using Redis
 * Provides caching for token verification to reduce database lookups
 * Implementation follows authentication improvements plan (Phase 2)
 */
import { getStorage, isRedisConnectionAvailable } from '../config/redis';
import { logger } from './logger';
import { TokenPayload } from './jwt-rs256';
import crypto from 'crypto';

// Prefix for cache keys to avoid collisions with other services
const CACHE_PREFIX = 'auth:token:';
const BLACKLIST_PREFIX = 'auth:blacklist:';

// Get TTL from environment variable or use default (5 minutes)
const CACHE_TTL = parseInt(process.env.REDIS_TOKEN_CACHE_TTL || '300', 10);

// Log Redis availability status on module load
if (isRedisConnectionAvailable()) {
  logger.info('Using Redis for distributed token caching');
} else {
  logger.warn('Redis unavailable, using memory fallback for token caching');
}

/**
 * Cache the result of a token verification with enhanced security features
 * Stores user verification data in Redis to reduce database lookups
 *
 * @param userId User ID from token
 * @param userData User data to cache
 * @param tokenId Optional JWT ID for additional security
 * @param tokenHash Hash of the full token for session isolation
 * @returns true if cached successfully, false otherwise
 */
export const cacheVerification = async (
  userId: string,
  userData: Record<string, any>,
  tokenId?: string,
  tokenHash?: string
): Promise<boolean> => {
  try {
    const storage = getStorage();

    // CRITICAL FIX: Always use token-specific cache keys to prevent session conflicts
    // Generate a unique cache key that includes token information
    let key: string;

    if (tokenId) {
      // Use JWT ID for backend tokens
      key = `${CACHE_PREFIX}${userId}:${tokenId}`;
    } else if (tokenHash) {
      // Use token hash for Supabase tokens or when JWT ID is not available
      key = `${CACHE_PREFIX}${userId}:${tokenHash}`;
    } else {
      // SECURITY FIX: Never use user-only cache keys as they cause session conflicts
      // Generate a unique session identifier based on timestamp and random data
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
      key = `${CACHE_PREFIX}${userId}:${sessionId}`;
      logger.warn(`No token ID or hash provided for user ${userId}, using temporary session key`);
    }
    
    // Add metadata to the cached data
    const cacheData = {
      ...userData,
      _meta: {
        cached_at: Date.now(),
        expires_at: Date.now() + (CACHE_TTL * 1000),
        source: 'token_verification'
      }
    };
    
    // Store user data as JSON string with expiry
    await storage.set(key, JSON.stringify(cacheData));
    await storage.expire(key, CACHE_TTL);
    
    logger.debug(`Cached verification data for user ${userId}${tokenId ? ' with tokenId' : ''}`);
    return true;
  } catch (error) {
    logger.error(`Error caching verification data: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
};

/**
 * Get cached user verification data with enhanced session isolation
 * Retrieves cached verification data using token-specific keys only
 *
 * @param userId User ID from token
 * @param tokenId Optional JWT ID for backend tokens
 * @param tokenHash Optional token hash for Supabase tokens
 * @returns Cached user data or null if not found
 */
export const getCachedVerification = async (
  userId: string,
  tokenId?: string,
  tokenHash?: string
): Promise<Record<string, any> | null> => {
  try {
    const storage = getStorage();
    
    // ENHANCED SESSION ISOLATION: Use token-specific cache keys only
    let cacheKey: string | null = null;

    if (tokenId) {
      // For backend tokens with JWT ID
      cacheKey = `${CACHE_PREFIX}${userId}:${tokenId}`;
    } else if (tokenHash) {
      // For Supabase tokens with hash
      cacheKey = `${CACHE_PREFIX}${userId}:${tokenHash}`;
    } else {
      // SECURITY FIX: No fallback to user-only cache to prevent session conflicts
      logger.debug(`No token ID or hash provided for user ${userId}, skipping cache lookup`);
      return null;
    }

    const cachedData = await storage.get(cacheKey);

    if (cachedData) {
      try {
        const parsed = JSON.parse(cachedData);
        // Check if cache is still valid based on metadata
        if (parsed._meta && parsed._meta.expires_at && parsed._meta.expires_at > Date.now()) {
          logger.debug(`Using token-specific cache for user ${userId} with key type: ${tokenId ? 'JWT ID' : 'Token Hash'}`);
          return parsed;
        } else {
          // Cache expired, delete it
          await storage.del(cacheKey);
          logger.debug(`Cache expired for user ${userId}, deleted key`);
        }
      } catch (e) {
        logger.warn(`Error parsing cached data for user ${userId}: ${e instanceof Error ? e.message : String(e)}`);
        // Delete corrupted cache entry
        await storage.del(cacheKey);
      }
    }
    
    // SECURITY FIX: Remove fallback to userId-only cache to prevent session conflicts
    // This was causing cross-user session contamination when multiple users were logged in
    logger.debug(`No token-specific cache found for user ${userId}, requiring fresh verification`);
    return null;
  } catch (error) {
    logger.error(`Error getting cached verification: ${error instanceof Error ? error.message : String(error)}`);
    return null;
  }
};

/**
 * Invalidate cached verification data for a user
 * Used when user data changes or during logout
 * 
 * @param userId User ID from token
 * @returns true if invalidated successfully, false otherwise
 */
export const invalidateCache = async (userId: string): Promise<boolean> => {
  try {
    const storage = getStorage();
    const key = `${CACHE_PREFIX}${userId}`;

    await storage.del(key);

    logger.debug(`Invalidated cached verification data for user ${userId}`);
    return true;
  } catch (error) {
    logger.error(`Error invalidating cached verification data: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
};

/**
 * Clear all cache entries for a specific user to prevent session conflicts
 * This removes both legacy user-only cache entries and helps with session isolation
 *
 * @param userId User ID to clear cache for
 * @returns true if cleared successfully, false otherwise
 */
export const clearUserCacheEntries = async (userId: string): Promise<boolean> => {
  try {
    const storage = getStorage();

    // Clear legacy user-only cache entry (if it exists)
    const legacyKey = `${CACHE_PREFIX}${userId}`;
    await storage.del(legacyKey);

    // Note: We can't easily clear all token-specific entries without scanning all keys
    // This is acceptable as token-specific entries will expire naturally
    // and the blacklist prevents their use

    logger.debug(`Cleared cache entries for user ${userId}`);
    return true;
  } catch (error) {
    logger.error(`Error clearing user cache entries: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
};

/**
 * Add a token to the blacklist
 * Used for revoked tokens or during logout
 * Implemented with Redis for distributed token revocation
 * 
 * @param token JWT token to blacklist
 * @param expirySeconds How long to keep the token in the blacklist (should match token expiry)
 * @returns true if blacklisted successfully, false otherwise
 */
export const blacklistToken = async (
  token: string,
  expirySeconds: number = CACHE_TTL
): Promise<boolean> => {
  try {
    const storage = getStorage();
    // Extract token ID and expiry if possible for better tracking
    let tokenMeta: { jti?: string; exp?: number } = {};
    
    try {
      // Try to extract token metadata without full verification
      const parts = token.split('.');
      if (parts.length === 3) {
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        tokenMeta.jti = payload.jti;
        tokenMeta.exp = payload.exp;
        
        // If token has expiry, use that instead of passed value (more precise)
        if (tokenMeta.exp) {
          const now = Math.floor(Date.now() / 1000);
          expirySeconds = Math.max(tokenMeta.exp - now, 60); // At least 60 seconds
        }
      }
    } catch (e) {
      // Failed to extract metadata, just continue with the hash
      logger.debug(`Failed to extract token metadata: ${e instanceof Error ? e.message : String(e)}`);
    }
    
    // Hash the token for storage
    const hash = hashToken(token);
    const key = `${BLACKLIST_PREFIX}${hash}`;
    
    // Store token in blacklist with metadata
    await storage.set(key, JSON.stringify({
      blacklisted_at: Date.now(),
      jti: tokenMeta.jti || 'unknown',
      type: 'revoked_token'
    }));
    await storage.expire(key, expirySeconds);
    
    logger.debug(`Added token to blacklist with expiry ${expirySeconds}s${tokenMeta.jti ? ' (jti: ' + tokenMeta.jti + ')' : ''}`);
    return true;
  } catch (error) {
    logger.error(`Error blacklisting token: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
};

/**
 * Check if a token is blacklisted
 * Uses Redis for distributed token revocation checking
 * 
 * @param token JWT token to check
 * @returns true if blacklisted, false otherwise
 */
export const isTokenBlacklisted = async (token: string): Promise<boolean> => {
  try {
    const storage = getStorage();
    const hash = hashToken(token);
    const key = `${BLACKLIST_PREFIX}${hash}`;
    
    const result = await storage.get(key);
    
    if (result !== null) {
      try {
        // Parse the blacklist data for logging/metrics
        const blacklistData = JSON.parse(result);
        const timeSinceBlacklist = Date.now() - (blacklistData.blacklisted_at || 0);
        logger.debug(`Token is blacklisted (jti: ${blacklistData.jti || 'unknown'}, blacklisted ${Math.floor(timeSinceBlacklist / 1000)}s ago)`);
      } catch {
        // Old format or parse error, just log that it's blacklisted
        logger.debug(`Token is blacklisted (hash: ${hash.substring(0, 8)}...)`);
      }
      return true;
    }
    
    return false;
  } catch (error) {
    logger.error(`Error checking token blacklist: ${error instanceof Error ? error.message : String(error)}`);
    // Default to not blacklisted on error to prevent locking users out
    return false;
  }
};

/**
 * Hash a token for blacklist storage
 * We don't store the actual token for security reasons
 * 
 * @param token JWT token to hash
 * @returns Hashed token
 */
const hashToken = (token: string): string => {
  let hash = 0;
  for (let i = 0; i < token.length; i++) {
    const char = token.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString(16);
};

/**
 * Rate limiting middleware to prevent brute force attacks
 * Uses in-memory storage for development, should use Redis in production
 * 
 * This middleware supports both configurable and predefined rate limiters for various endpoints
 */

import { Request, Response, NextFunction } from 'express';
import { config } from '../config';
import { logger } from '../utils/logger';
import { getStorage } from '../config/redis';

/**
 * Helper function to get rate limit data from Redis or fallback to memory
 * @param key The rate limit key (usually IP + route)
 * @returns The rate limit data or null if not found
 */
const getRateLimitData = async (key: string): Promise<{ count: number; resetTime: number } | null> => {
  try {
    const storage = getStorage();
    const data = await storage.get(`rate:${key}`);
    if (!data) return null;
    
    return JSON.parse(data);
  } catch (error) {
    logger.error(`Redis rate limit error (falling back to memory): ${error instanceof Error ? error.message : String(error)}`);
    // Fall back to memory store if <PERSON><PERSON> is unavailable
    return memoryStore[key] || null;
  }
};

/**
 * Helper function to set rate limit data in Redis or fallback to memory
 * @param key The rate limit key (usually IP + route)
 * @param data The rate limit data
 * @param expireMs Expiration time in milliseconds
 */
const setRateLimitData = async (key: string, data: { count: number; resetTime: number }, expireMs: number): Promise<void> => {
  try {
    const storage = getStorage();
    await storage.set(`rate:${key}`, JSON.stringify(data));
    await storage.expire(`rate:${key}`, Math.ceil(expireMs / 1000));
  } catch (error) {
    logger.error(`Redis rate limit error (falling back to memory): ${error instanceof Error ? error.message : String(error)}`);
    // Fall back to memory store if Redis is unavailable
    memoryStore[key] = data;
  }
};

/**
 * Helper function to increment rate limit count in Redis or fallback to memory
 * @param key The rate limit key (usually IP + route)
 * @returns The new count
 */
const incrementRateLimitCount = async (key: string): Promise<number> => {
  try {
    const storage = getStorage();
    const count = await storage.incr(`rate:${key}:count`);
    return count;
  } catch (error) {
    logger.error(`Redis rate limit increment error (falling back to memory): ${error instanceof Error ? error.message : String(error)}`);
    // Fall back to memory store if Redis is unavailable
    if (!memoryStore[key]) {
      memoryStore[key] = { count: 0, resetTime: Date.now() + 60000 };
    }
    memoryStore[key].count += 1;
    return memoryStore[key].count;
  }
};

// In-memory fallback store for when Redis is unavailable
interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

const memoryStore: RateLimitStore = {};

// Cleanup old entries in memory store periodically
setInterval(() => {
  const now = Date.now();
  for (const key in memoryStore) {
    if (memoryStore[key].resetTime < now) {
      delete memoryStore[key];
    }
  }
}, 60000); // Cleanup every minute

/**
 * Rate limiting middleware with configurable options
 * @param options Rate limiting options
 */
export interface RateLimitOptions {
  windowMs: number;  // Time window in milliseconds
  max: number;       // Maximum requests in the time window
  message?: string;  // Custom error message
}

export const rateLimitMiddleware = (options: RateLimitOptions) => {
  const { windowMs, max, message = 'Too many requests, please try again later.' } = options;
  
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const ip = req.ip || req.socket.remoteAddress || 'unknown';
      const key = `${ip}:${req.path}`; // Include path for more granular limiting
      const now = Date.now();
      
      // Get rate limit data from Redis or initialize if not exists
      let data = await getRateLimitData(key);
      
      // Handle window expiration or new entry
      if (!data || now > data.resetTime) {
        data = {
          count: 0,
          resetTime: now + windowMs
        };
      }
      
      // Increment request count
      data.count++;
      
      // Store updated data
      await setRateLimitData(key, data, windowMs);
      
      // Add rate limit headers
      res.setHeader('X-RateLimit-Limit', max.toString());
      res.setHeader('X-RateLimit-Remaining', Math.max(0, max - data.count).toString());
      res.setHeader('X-RateLimit-Reset', Math.floor(data.resetTime / 1000).toString());
      
      // Check if rate limit exceeded
      if (data.count > max) {
        logger.warn(`Rate limit exceeded for ${ip} on ${req.path}`);
        return res.status(429).json({
          success: false,
          error: 'RATE_LIMIT_EXCEEDED',
          message
        });
      }
      
      next();
    } catch (error) {
      // If rate limiting fails, allow the request to proceed but log the error
      logger.error(`Rate limit error: ${error instanceof Error ? error.message : String(error)}`);
      next();
    }
  };
}

/**
 * Apply rate limiting to auth routes
 */
export const authRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Skip rate limiting in development mode
    if (process.env.NODE_ENV === 'development') {
      return next();
    }

    const ip = req.ip || req.socket.remoteAddress || 'unknown';
    
    // Get configured rate limit settings
    const windowMs = config.server.rateLimit.windowMs;
    const maxRequests = config.server.rateLimit.max;
    
    // Get current time
    const now = Date.now();
    
    // Get rate limit data from Redis or initialize
    let rateLimitData = await getRateLimitData(`auth:${ip}`);
    
    // Initialize or reset if expired
    if (!rateLimitData || rateLimitData.resetTime < now) {
      rateLimitData = {
        count: 0,
        resetTime: now + windowMs
      };
    }
    
    // Increment request count
    rateLimitData.count++;
    
    // Store in Redis
    await setRateLimitData(`auth:${ip}`, rateLimitData, windowMs);
    
    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', maxRequests.toString());
    res.setHeader('X-RateLimit-Remaining', Math.max(0, maxRequests - rateLimitData.count).toString());
    res.setHeader('X-RateLimit-Reset', Math.floor(rateLimitData.resetTime / 1000).toString());
    
    // Check if rate limit exceeded
    if (rateLimitData.count > maxRequests) {
      logger.warn(`Auth rate limit exceeded for IP: ${ip}`);
      return res.status(429).json({
        error: 'Too many requests, please try again later',
        retryAfter: Math.floor((rateLimitData.resetTime - now) / 1000)
      });
    }
    
    next();
  } catch (error) {
    // If rate limiting fails, allow request but log error
    logger.error(`Auth rate limit error: ${error instanceof Error ? error.message : String(error)}`);
    next();
  }
};

/**
 * Stricter rate limit for login attempts (helps prevent brute force)
 */
export const loginRateLimit = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Skip rate limiting in development mode
    if (process.env.NODE_ENV === 'development') {
      return next();
    }

    const ip = req.ip || req.socket.remoteAddress || 'unknown';
    const loginWindowMs = 15 * 60 * 1000; // 15 minutes
    const maxLoginAttempts = 5; // Very strict limit for login attempts
    
    // Get current time
    const now = Date.now();
    
    // Get rate limit data from Redis or initialize
    let rateLimitData = await getRateLimitData(`login:${ip}`);
    
    // Initialize or reset if expired
    if (!rateLimitData || rateLimitData.resetTime < now) {
      rateLimitData = {
        count: 0,
        resetTime: now + loginWindowMs
      };
    }
    
    // Increment login attempt count
    rateLimitData.count++;
    
    // Store in Redis
    await setRateLimitData(`login:${ip}`, rateLimitData, loginWindowMs);
    
    // Set headers
    res.setHeader('X-RateLimit-Limit', maxLoginAttempts.toString());
    res.setHeader('X-RateLimit-Remaining', Math.max(0, maxLoginAttempts - rateLimitData.count).toString());
    res.setHeader('X-RateLimit-Reset', Math.floor(rateLimitData.resetTime / 1000).toString());
    
    // Check if exceeded
    if (rateLimitData.count > maxLoginAttempts) {
      // Log the security event
      logger.warn(`Login rate limit exceeded for IP: ${ip}`);
      
      // Add artificial delay to slow down brute force attempts
      setTimeout(() => {
        res.status(429).json({
          error: 'Too many login attempts, please try again later',
          retryAfter: Math.floor((rateLimitData.resetTime - now) / 1000)
        });
      }, 1000); // 1 second delay
      return;
    }
    
    next();
  } catch (error) {
    // If rate limiting fails, allow the request but log the error
    logger.error(`Login rate limit error: ${error instanceof Error ? error.message : String(error)}`);
    next();
  }
};

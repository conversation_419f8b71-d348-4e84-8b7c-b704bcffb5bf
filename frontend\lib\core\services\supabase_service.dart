import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static const String _supabaseUrl = 'https://johjwarjusfahxidemjd.supabase.co';
  static const String _supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpvaGp3YXJqdXNmYWh4aWRlbWpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMDM3NjcsImV4cCI6MjA2MTU3OTc2N30.F77CeDDNeQqRjhhUjhcB_322RLaP4pBx6-2L0VZ7gJY';

  static Future<void> initialize() async {
    try {
      await Supabase.initialize(
        url: _supabaseUrl,
        anonKey: _supabaseAnonKey,
        debug: !kReleaseMode,
      );
      debugPrint('Supabase initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Supabase: $e');
      // Fall back gracefully - app will still work with primary backend
    }
  }

  // Helper getter for the Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;
}

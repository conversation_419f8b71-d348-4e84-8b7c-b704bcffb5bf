/**
 * Build script for WiggyZ backend
 * Compiles TypeScript and copies necessary files
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔨 Building WiggyZ backend...');

try {
  // Step 1: Compile TypeScript
  console.log('📦 Compiling TypeScript...');
  execSync('tsc -p .', { stdio: 'inherit' });
  
  // Step 2: Copy config files
  console.log('📁 Copying configuration files...');
  
  // Ensure dist/config directory exists
  const distConfigDir = path.join(__dirname, 'dist', 'config');
  if (!fs.existsSync(distConfigDir)) {
    fs.mkdirSync(distConfigDir, { recursive: true });
  }
  
  // Copy keys directory
  const srcKeysDir = path.join(__dirname, 'src', 'config', 'keys');
  const distKeysDir = path.join(distConfigDir, 'keys');
  
  if (fs.existsSync(srcKeysDir)) {
    // Create keys directory in dist
    if (!fs.existsSync(distKeysDir)) {
      fs.mkdirSync(distKeysDir, { recursive: true });
    }
    
    // Copy all files from src/config/keys to dist/config/keys
    const keyFiles = fs.readdirSync(srcKeysDir);
    keyFiles.forEach(file => {
      const srcFile = path.join(srcKeysDir, file);
      const distFile = path.join(distKeysDir, file);
      fs.copyFileSync(srcFile, distFile);
      console.log(`   ✅ Copied ${file}`);
    });
  }
  
  console.log('✅ Build completed successfully!');
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

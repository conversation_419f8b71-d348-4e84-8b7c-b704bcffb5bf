import { Request, Response } from 'express';
import { supabase } from '../../../config/supabase';
import bcrypt from 'bcrypt';
import { generateTokens } from '../../../utils/jwt-rs256';
import { logger } from '../../../utils/logger';
import { cacheVerification } from '../../../utils/tokenCache';
import { localUserStore } from '../../../utils/local-store';
import { checkAccountRateLimiting, recordLoginFailure, resetLoginFailures } from '../../../middleware/advancedRateLimit';
import { generateRefreshToken } from '../../../utils/refreshTokenManager';
import { v4 as uuidv4 } from 'uuid';

export const loginUser = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Validate request data
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }
    
    // Skip rate limiting in development mode for testing
    if (process.env.NODE_ENV !== 'development') {
      // Check for account-specific rate limiting to prevent credential stuffing
      const { isLimited, retryAfter } = await checkAccountRateLimiting(email);
      if (isLimited) {
        logger.warn(`Login attempt blocked due to rate limiting for account: ${email}`);
        return res.status(429).json({
          error: 'Too many login attempts, please try again later',
          retryAfter: Math.ceil(retryAfter / 60), // Convert to minutes for the client
          code: 'RATE_LIMITED'
        });
      }
    }

    // First attempt: Try Supabase for login
    try {
      // Login with Supabase Auth
      const { data, error: loginError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      // Handle Supabase auth errors
      if (loginError) {
        if (loginError.message.includes('connection unavailable')) {
          // Detected our custom error - proceed to fallback
          throw new Error('Supabase connection unavailable');
        }

        // In development mode, try local fallback for authentication failures
        if (process.env.NODE_ENV === 'development') {
          console.log('Development mode: Attempting local fallback for authentication failure');
          throw new Error('Development mode: Force local fallback');
        }

        await recordLoginFailure(email);
        return res.status(401).json({ error: loginError.message });
      }

      // Get user profile and role with verification status
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, name, role, is_verified, is_blocked, blocked_reason')
        .eq('id', data.user.id)
        .single();

      if (userError) {
        logger.error(`Error fetching user profile: ${userError.message}`);
        return res.status(400).json({ error: 'Error fetching user profile' });
      }

      if (!userData) {
        logger.error(`No user data found for ${data.user.id}`);
        return res.status(404).json({ error: 'User not found' });
      }

      // Check if user is verified
      if (!userData.is_verified) {
        return res.status(401).json({ error: 'Your account is not verified yet' });
      }

      // Check if user is blocked
      if (userData.is_blocked) {
        return res.status(403).json({ 
          error: 'Your account is blocked', 
          reason: userData.blocked_reason || 'No reason provided'
        });
      }

      // Update last login timestamp
      await supabase
        .from('users')
        .update({ last_login_at: new Date(), last_active: new Date() })
        .eq('id', data.user.id);

      // Generate JWT tokens
      const tokenPayload = {
        userId: data.user.id,
        email: data.user.email,
        role: userData.role,
        isActive: true,
        isVerified: userData.is_verified
      };

      // Generate access token using RS256 algorithm
      const { accessToken } = generateTokens(tokenPayload);
      
      // Generate refresh token with token rotation support
      const refreshTokenData = await generateRefreshToken({
        id: data.user.id,
        role: userData.role
      });
      
      // Reset any login failure counters since login was successful
      await resetLoginFailures(email);

      // Cache the verification data for faster token validation
      await cacheVerification(data.user.id, {
        id: data.user.id,
        role: userData.role,
        is_verified: userData.is_verified
      });

      logger.info(`User ${data.user.id} logged in successfully with Supabase (RS256 tokens)`);

      return res.status(200).json({
        message: 'Login successful with Supabase',
        access_token: accessToken,
        refresh_token: refreshTokenData.token,
        family_id: refreshTokenData.family_id,
        user: {
          id: data.user.id,
          email: data.user.email,
          name: userData.name || data.user.email.split('@')[0],
          role: userData.role
        }
      });
    } catch (supabaseError: any) {
      // Fallback to local authentication if Supabase is down
      console.warn('Supabase login failed, using local fallback:', supabaseError?.message || 'Unknown error');

      logger.warn(`Supabase login failed, using local fallback: ${supabaseError?.message || 'Unknown error'}`);

      // Seed test users if local store is empty (development mode)
      if (process.env.NODE_ENV === 'development' && Object.keys(localUserStore).length === 0) {
        await seedTestUsersSync();
      }

      // Try to find user in local store
      const user = Object.values(localUserStore || {}).find((u: any) => u.email === email);

      if (!user) {
        // Record login failure to implement progressive backoff
        await recordLoginFailure(email);
        return res.status(401).json({ error: 'Invalid email or password' });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);

      if (!isValidPassword) {
        // Record login failure to implement progressive backoff
        await recordLoginFailure(email);
        return res.status(401).json({ error: 'Invalid email or password' });
      }

      // Update last login timestamp (in memory only)
      user.last_login_at = new Date();
      user.last_active = new Date();

      // Set verification status - for local users default to verified
      const isVerified = user.is_verified !== false;
      const isActive = user.is_active !== false;

      // Generate JWT tokens with user claims for client-side validation
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        role: user.role,
        isActive: isActive,
        isVerified: isVerified
      };

      // Generate tokens using RS256 algorithm
      const { accessToken, refreshToken } = generateTokens(tokenPayload);

      // Generate refresh token with token rotation support
      const refreshTokenData = await generateRefreshToken({
        id: user.id,
        role: user.role
      });
      
      await resetLoginFailures(email);

      // Cache the verification data for faster token validation
      await cacheVerification(user.id, {
        id: user.id,
        role: user.role,
        is_verified: user.is_verified
      });

      logger.info(`User ${user.id} logged in successfully with local fallback (RS256 tokens)`);

      return res.status(200).json({
        message: 'Login successful with local authentication',
        access_token: accessToken,
        refresh_token: refreshTokenData.token,
        family_id: refreshTokenData.family_id,
        user: {
          id: user.id,
          email: user.email,
          name: user.name || user.email,
          role: user.role,
          isVerified: user.is_verified !== false,
          isActive: user.is_active !== false
        }
      });
    }
  } catch (err) {
    console.error('Login error:', err);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Synchronously seed test users for development
 */
async function seedTestUsersSync(): Promise<void> {
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Test User 1',
      role: 'player',
      username: 'testuser1'
    },
    {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Test User 2',
      role: 'player',
      username: 'testuser2'
    },
    {
      email: '<EMAIL>',
      password: 'AdminPassword123!',
      name: 'Admin User',
      role: 'admin',
      username: 'admin'
    }
  ];

  logger.info('💾 Seeding test users in local store...');

  for (const user of testUsers) {
    try {
      // Generate unique user ID
      const userId = uuidv4();

      // Hash password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(user.password, saltRounds);

      // Store user in local memory
      localUserStore[userId] = {
        id: userId,
        email: user.email,
        name: user.name,
        username: user.username,
        password: hashedPassword,
        role: user.role,
        is_verified: true,
        is_active: true,
        wallet_balance: 1000,
        reward_points: 100,
        honor_score: 50,
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: null,
        last_active: new Date()
      };

      logger.info(`✅ Seeded local test user: ${user.email}`);
    } catch (error) {
      logger.error(`❌ Error seeding local user ${user.email}:`, error);
    }
  }

  logger.info(`📊 Local store now has ${Object.keys(localUserStore).length} users`);
}

{"version": 2, "builds": [{"src": "dist/vercel.js", "use": "@vercel/node", "config": {"maxLambdaSize": "50mb", "maxDuration": 30}}], "rewrites": [{"source": "/health", "destination": "/dist/vercel.js"}, {"source": "/api/v1/(.*)", "destination": "/dist/vercel.js"}, {"source": "/(.*)", "destination": "/dist/vercel.js"}], "headers": [{"source": "/api/v1/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Idempotency-Key"}]}, {"source": "/health", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}]}], "env": {"NODE_ENV": "production"}, "regions": ["iad1"]}
services:
  - type: web
    name: wiggyz-backend
    env: node
    plan: starter
    region: oregon
    buildCommand: npm install && npm run build
    startCommand: npm start
    rootDir: backend
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: JWT_REFRESH_SECRET
        sync: false
      - key: RAZORPAY_KEY_ID
        sync: false
      - key: RAZ<PERSON><PERSON>Y_KEY_SECRET
        sync: false
    healthCheckPath: /health

// Debug script to check local store contents
const { localUserStore } = require('./dist/utils/local-store');

console.log('🔍 Debugging Local Store Contents');
console.log('================================');

console.log('Local store keys:', Object.keys(localUserStore));
console.log('Local store length:', Object.keys(localUserStore).length);

if (Object.keys(localUserStore).length > 0) {
  console.log('\n👥 Users in local store:');
  Object.values(localUserStore).forEach((user, index) => {
    console.log(`${index + 1}. Email: ${user.email}, ID: ${user.id}, Role: ${user.role}`);
  });
  
  // Test user lookup
  console.log('\n🔍 Testing user lookup:');
  const testEmail = '<EMAIL>';
  const foundUser = Object.values(localUserStore).find(u => u.email === testEmail);
  console.log(`Looking for: ${testEmail}`);
  console.log(`Found user:`, foundUser ? `Yes (ID: ${foundUser.id})` : 'No');
} else {
  console.log('\n❌ Local store is empty!');
}

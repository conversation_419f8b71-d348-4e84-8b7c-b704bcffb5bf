import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:wiggyz_app/features/wallet/services/wallet_service.dart';
import 'package:wiggyz_app/services/razorpay_web_service_interface.dart';

class RazorpayService {
  static final RazorpayService _instance = RazorpayService._internal();
  factory RazorpayService() => _instance;
  RazorpayService._internal();

  late Razorpay _razorpay;
  final WalletService _walletService = WalletService();
  final RazorpayWebService _webService = RazorpayWebService();

  // Callback functions
  Function(PaymentSuccessResponse)? _onPaymentSuccess;
  Function(PaymentFailureResponse)? _onPaymentError;
  Function(ExternalWalletResponse)? _onExternalWallet;

  void initialize() {
    if (kIsWeb) {
      print('Initializing Razorpay Web Service for web platform');
      _webService.initialize();
      return;
    }

    try {
      _razorpay = Razorpay();
      _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
      _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
      _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
      print('Razorpay mobile plugin initialized successfully');
    } catch (e) {
      print('Error initializing Razorpay: $e');
    }
  }

  void dispose() {
    if (!kIsWeb) {
      try {
        _razorpay.clear();
      } catch (e) {
        print('Error disposing Razorpay: $e');
      }
    }
  }

  Future<void> startPayment({
    required String orderId,
    required String keyId,
    required double amount,
    required String currency,
    required String name,
    required String description,
    required String userEmail,
    required String userPhone,
    required String userName,
    required Function(PaymentSuccessResponse) onSuccess,
    required Function(PaymentFailureResponse) onError,
    Function(ExternalWalletResponse)? onExternalWallet,
  }) async {
    // Store callbacks
    _onPaymentSuccess = onSuccess;
    _onPaymentError = onError;
    _onExternalWallet = onExternalWallet;

    var options = {
      'key': keyId,
      'amount': (amount * 100).toInt(), // Amount in paise
      'currency': currency,
      'name': 'WiggyZ',
      'description': description,
      'order_id': orderId,
      'prefill': {'contact': userPhone, 'email': userEmail, 'name': userName},
      'theme': {
        'color': '#FFCC00', // WiggyZ brand color
        'backdrop_color': 'rgba(0,0,0,0.6)',
      },
      'modal': {
        'ondismiss': () {
          print('Payment modal dismissed');
        },
        'confirm_close': true,
        'escape': true,
      },
      'retry': {'enabled': true, 'max_count': 3},
      'timeout': 900, // 15 minutes in seconds
      'readonly': {'email': false, 'contact': false, 'name': false},
      'hidden': {'email': false, 'contact': false},
      'method': {
        'card': true,
        'netbanking': true,
        'wallet': true,
        'upi': true,
        'emi': false,
      },
      'config': {
        'display': {
          'blocks': {
            'banks': {
              'name': 'Pay using Net Banking',
              'instruments': [
                {
                  'method': 'netbanking',
                  'banks': ['HDFC', 'ICICI', 'SBI', 'AXIS', 'KOTAK'],
                },
              ],
            },
            'other': {
              'name': 'Other Payment Methods',
              'instruments': [
                {
                  'method': 'card',
                  'issuers': ['VISA', 'MASTERCARD', 'AMEX'],
                },
                {'method': 'upi'},
              ],
            },
          },
          'sequence': ['block.banks', 'block.other'],
          'preferences': {'show_default_blocks': true},
        },
      },
      'send_sms_hash': true,
      'allow_rotation': true,
      'remember_customer': false,
    };

    // Check platform support before opening payment
    if (kIsWeb) {
      print(
        'Web platform detected - Razorpay Flutter plugin not fully supported on web',
      );
      _onPaymentError?.call(
        PaymentFailureResponse(
          1,
          'Payment not supported on web platform. Please use mobile app.',
          {'order_id': orderId, 'platform': 'web'},
        ),
      );
      return;
    }

    try {
      _razorpay.open(options);
    } catch (e) {
      print('Error starting Razorpay payment: $e');
      _onPaymentError?.call(
        PaymentFailureResponse(
          1, // Generic error code
          'Failed to start payment: $e',
          {'order_id': orderId},
        ),
      );
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    print('Payment Success: ${response.paymentId}');
    _onPaymentSuccess?.call(response);
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print('Payment Error: ${response.code} - ${response.message}');
    _onPaymentError?.call(response);
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print('External Wallet: ${response.walletName}');
    _onExternalWallet?.call(response);
  }

  Future<Map<String, dynamic>> verifyPaymentWithBackend({
    required String razorpayOrderId,
    required String razorpayPaymentId,
    required String razorpaySignature,
    required String transactionId,
  }) async {
    try {
      final paymentData = {
        'razorpay_order_id': razorpayOrderId,
        'razorpay_payment_id': razorpayPaymentId,
        'razorpay_signature': razorpaySignature,
        'transaction_id': transactionId,
      };

      return await _walletService.verifyPayment(paymentData);
    } catch (e) {
      print('Error verifying payment with backend: $e');
      rethrow;
    }
  }

  // Helper method to handle the complete payment flow
  Future<Map<String, dynamic>> processWalletTopUp({
    required BuildContext context,
    required double amount,
    required String currency,
    required String userEmail,
    required String userPhone,
    required String userName,
    String? transactionId, // Made optional - will be generated by backend
  }) async {
    // Use web service for web platform
    if (kIsWeb) {
      return await _webService.processWebWalletTopUp(
        context: context,
        amount: amount,
        currency: currency,
        userEmail: userEmail,
        userPhone: userPhone,
        userName: userName,
        transactionId: transactionId, // Pass null, web service will handle it
      );
    }

    try {
      // Step 1: Initiate top-up with backend to get Razorpay order
      final topUpData = {
        'amount': amount,
        'currency': currency,
        'payment_method': 'upi', // This will route to Razorpay
      };

      final topUpResponse = await _walletService.topUpWallet(topUpData);

      if (!topUpResponse.containsKey('data')) {
        throw Exception('Invalid response from server');
      }

      final responseData = topUpResponse['data'];
      final orderId = responseData['order_id'] ?? responseData['payment_token'];
      final keyId = responseData['key_id'];
      final backendTransactionId =
          responseData['transaction_id']; // Get transaction ID from backend

      if (orderId == null || keyId == null) {
        throw Exception('Missing order ID or key ID from server response');
      }

      if (backendTransactionId == null) {
        throw Exception('Missing transaction ID from server response');
      }

      // Use the transaction ID from backend
      final actualTransactionId = backendTransactionId;

      // Step 2: Start Razorpay payment
      final completer = Completer<Map<String, dynamic>>();

      await startPayment(
        orderId: orderId,
        keyId: keyId,
        amount: amount,
        currency: currency,
        name: userName,
        description: 'Wallet Top-up',
        userEmail: userEmail,
        userPhone: userPhone,
        userName: userName,
        onSuccess: (PaymentSuccessResponse response) async {
          try {
            // Step 3: Verify payment with backend
            final verificationResult = await verifyPaymentWithBackend(
              razorpayOrderId: response.orderId ?? orderId,
              razorpayPaymentId: response.paymentId!,
              razorpaySignature: response.signature!,
              transactionId:
                  actualTransactionId, // Use the transaction ID from backend
            );

            completer.complete(verificationResult);
          } catch (e) {
            completer.completeError(e);
          }
        },
        onError: (PaymentFailureResponse response) {
          completer.completeError(
            Exception('Payment failed: ${response.message}'),
          );
        },
        onExternalWallet: (ExternalWalletResponse response) {
          // Handle external wallet if needed
          print('External wallet selected: ${response.walletName}');
        },
      );

      return await completer.future;
    } catch (e) {
      print('Error in processWalletTopUp: $e');
      rethrow;
    }
  }

  // Error handling helper
  static String getErrorMessage(PaymentFailureResponse response) {
    switch (response.code) {
      case Razorpay.NETWORK_ERROR:
        return 'Network error. Please check your internet connection.';
      case 2: // Invalid credentials error code
        return 'Payment configuration error. Please try again.';
      case Razorpay.PAYMENT_CANCELLED:
        return 'Payment was cancelled.';
      case Razorpay.TLS_ERROR:
        return 'Security error. Please try again.';
      case Razorpay.INCOMPATIBLE_PLUGIN:
        return 'App compatibility issue. Please update the app.';
      case Razorpay.UNKNOWN_ERROR:
      default:
        return response.message ?? 'Payment failed. Please try again.';
    }
  }

  // Payment method availability check
  static bool isPaymentMethodAvailable(String method) {
    // This can be enhanced to check device capabilities
    switch (method.toLowerCase()) {
      case 'upi':
      case 'card':
      case 'netbanking':
      case 'wallet':
        return true;
      default:
        return false;
    }
  }

  // Get supported payment methods
  static List<String> getSupportedPaymentMethods() {
    return ['UPI', 'Cards', 'Net Banking', 'Wallets'];
  }
}

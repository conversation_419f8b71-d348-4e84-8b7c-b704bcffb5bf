/**
 * API Configuration for WiggyZ Admin Dashboard
 * Centralized configuration for backend API endpoints and settings
 */

export class ApiConfig {
  // Base URL for the backend API
  static get baseUrl(): string {
    // Environment-based API URL configuration
    // Development: http://127.0.0.1:5000/api/v1
    // Production: https://wiggyz-backend.onrender.com/api/v1
    return process.env.NEXT_PUBLIC_API_URL || 'https://wiggyz-backend.onrender.com/api/v1';
  }

  // Environment detection
  static get isDevelopment(): boolean {
    return this.baseUrl.includes('127.0.0.1') || this.baseUrl.includes('localhost');
  }

  static get isProduction(): boolean {
    return this.baseUrl.includes('onrender.com');
  }

  static get environment(): string {
    return process.env.NEXT_PUBLIC_ENVIRONMENT || (this.isDevelopment ? 'development' : 'production');
  }

  // Connection timeout settings
  static get connectionTimeout(): number {
    return this.isDevelopment ? 30000 : 15000; // 30s dev, 15s prod
  }

  static get receiveTimeout(): number {
    return this.isDevelopment ? 30000 : 15000;
  }

  // API Endpoints
  static get endpoints() {
    return {
      // Authentication
      auth: {
        login: '/auth/login',
        logout: '/auth/logout',
        refresh: '/auth/refresh',
        me: '/auth/me',
        verifyToken: '/auth/verify-token'
      },

      // User Management
      users: {
        list: '/admin/users',
        create: '/admin/users',
        update: (id: string) => `/admin/users/${id}`,
        delete: (id: string) => `/admin/users/${id}`,
        profile: (id: string) => `/admin/users/${id}/profile`
      },

      // Match Management
      matches: {
        list: '/matches',
        create: '/matches',
        get: (id: string) => `/matches/${id}`,
        update: (id: string) => `/matches/${id}`,
        delete: (id: string) => `/matches/${id}`,
        participants: (id: string) => `/matches/${id}/participants`,
        results: (id: string) => `/matches/${id}/results`,
        assignWinner: (id: string) => `/matches/${id}/assign-winner`,
        pendingResults: '/matches/admin/pending-results',
        verifyResult: (resultId: string) => `/matches/admin/results/${resultId}/verify`,
        distributeRewards: (id: string) => `/matches/${id}/distribute-rewards`
      },

      // Tournament Management
      tournaments: {
        list: '/tournaments',
        create: '/tournaments',
        get: (id: string) => `/tournaments/${id}`,
        update: (id: string) => `/tournaments/${id}`,
        delete: (id: string) => `/tournaments/${id}`,
        participants: (id: string) => `/tournaments/${id}/participants`,
        matches: (id: string) => `/tournaments/${id}/matches`,
        leaderboard: (id: string) => `/tournaments/${id}/leaderboard`,
        register: '/tournaments/register'
      },

      // Wallet Management
      wallet: {
        transactions: '/wallet/transactions',
        topup: '/wallet/topup',
        withdraw: '/wallet/withdraw',
        verifyPayment: '/wallet/verify-payment',
        adminWithdrawals: '/admin/withdrawals',
        adminNotifications: '/admin/notifications'
      },

      // Rewards System
      rewards: {
        list: '/rewards',
        create: '/rewards',
        update: (id: string) => `/rewards/${id}`,
        delete: (id: string) => `/rewards/${id}`,
        userAchievements: '/rewards/achievements'
      },

      // Support System
      support: {
        messages: '/support/messages',
        message: (id: string) => `/support/messages/${id}`,
        updateStatus: (id: string) => `/support/messages/${id}/status`
      },

      // Games
      games: {
        list: '/games',
        create: '/games',
        update: (id: string) => `/games/${id}`,
        delete: (id: string) => `/games/${id}`
      }
    };
  }

  // Helper method to build full URL
  static buildUrl(endpoint: string): string {
    // Remove leading slash if present to avoid double slashes
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${this.baseUrl}/${cleanEndpoint}`;
  }

  // Helper method to get request headers
  static getHeaders(token?: string): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  // Helper method for fetch configuration
  static getFetchConfig(token?: string, method: string = 'GET', body?: any): RequestInit {
    const config: RequestInit = {
      method,
      headers: this.getHeaders(token),
      timeout: this.connectionTimeout
    };

    if (body && method !== 'GET') {
      config.body = JSON.stringify(body);
    }

    return config;
  }

  // Debug information
  static getDebugInfo() {
    return {
      baseUrl: this.baseUrl,
      environment: this.environment,
      isDevelopment: this.isDevelopment,
      isProduction: this.isProduction,
      connectionTimeout: this.connectionTimeout,
      receiveTimeout: this.receiveTimeout
    };
  }
}

export default ApiConfig;

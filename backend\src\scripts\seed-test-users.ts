/**
 * Script to seed test users for development and testing
 * This creates test users in both Supabase and local store for fallback
 */

import { supabase } from '../config/supabase';
import { localUserStore } from '../utils/local-store';
import { logger } from '../utils/logger';
import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

interface TestUser {
  email: string;
  password: string;
  name: string;
  role: string;
  username: string;
}

const testUsers: TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Test User 1',
    role: 'player',
    username: 'testuser1'
  },
  {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Test User 2',
    role: 'player',
    username: 'testuser2'
  },
  {
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    name: 'Admin User',
    role: 'admin',
    username: 'admin'
  }
];

/**
 * Create test users in Supabase
 */
async function createSupabaseTestUsers(): Promise<void> {
  logger.info('👥 Creating test users in Supabase...');

  for (const user of testUsers) {
    try {
      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('id, email')
        .eq('email', user.email)
        .single();

      if (existingUser) {
        logger.info(`ℹ️  User already exists in Supabase: ${user.email}`);
        continue;
      }

      // Create auth user
      const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
        email: user.email,
        password: user.password,
        email_confirm: true
      });

      if (authError) {
        logger.warn(`⚠️  Could not create auth user ${user.email}:`, authError.message);
        continue;
      }

      if (!authUser.user) {
        logger.warn(`⚠️  No user data returned for ${user.email}`);
        continue;
      }

      // Create user profile
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: authUser.user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          is_verified: true,
          is_active: true,
          wallet_balance: 1000, // Give test users some initial balance
          reward_points: 100,
          honor_score: 50,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        logger.warn(`⚠️  Could not create user profile ${user.email}:`, profileError.message);
      } else {
        logger.info(`✅ Created Supabase test user: ${user.email}`);
      }
    } catch (error) {
      logger.error(`❌ Error creating Supabase user ${user.email}:`, error);
    }
  }
}

/**
 * Create test users in local store (fallback)
 */
async function createLocalTestUsers(): Promise<void> {
  logger.info('💾 Creating test users in local store...');

  for (const user of testUsers) {
    try {
      // Check if user already exists in local store
      const existingUser = Object.values(localUserStore).find((u: any) => u.email === user.email);
      
      if (existingUser) {
        logger.info(`ℹ️  User already exists in local store: ${user.email}`);
        continue;
      }

      // Generate unique user ID
      const userId = uuidv4();
      
      // Hash password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(user.password, saltRounds);
      
      // Store user in local memory
      localUserStore[userId] = {
        id: userId,
        email: user.email,
        name: user.name,
        username: user.username,
        password: hashedPassword,
        role: user.role,
        is_verified: true,
        is_active: true,
        wallet_balance: 1000,
        reward_points: 100,
        honor_score: 50,
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: null,
        last_active: new Date()
      };

      logger.info(`✅ Created local test user: ${user.email}`);
    } catch (error) {
      logger.error(`❌ Error creating local user ${user.email}:`, error);
    }
  }
}

/**
 * Main seeding function
 */
export async function seedTestUsers(): Promise<void> {
  try {
    logger.info('🌱 Starting test user seeding...');

    // Try to create users in Supabase first
    await createSupabaseTestUsers();

    // Always create users in local store as fallback
    await createLocalTestUsers();

    logger.info('✅ Test user seeding completed successfully!');
    logger.info(`📊 Local store now has ${Object.keys(localUserStore).length} users`);
    
    // Log the test users for reference
    logger.info('🔑 Test user credentials:');
    testUsers.forEach(user => {
      logger.info(`   ${user.email} / ${user.password} (${user.role})`);
    });

  } catch (error) {
    logger.error(`❌ Error seeding test users: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

// Run the seeding if this script is executed directly
if (require.main === module) {
  seedTestUsers()
    .then(() => {
      logger.info('🎉 Test user seeding script completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Test user seeding script failed:', error);
      process.exit(1);
    });
}

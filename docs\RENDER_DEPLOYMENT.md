# WiggyZ Backend Render.com Deployment Guide

## Overview

This guide covers deploying the WiggyZ backend to Render.com as an alternative to Vercel when authentication protection issues prevent public API access.

## Why Render.com?

- **Always-on hosting**: No cold starts like serverless functions
- **Public API access**: No authentication barriers for API endpoints
- **Native Node.js support**: Excellent for Express applications
- **Free tier**: Suitable for development and testing
- **Simple deployment**: Git-based deployment with automatic builds

## Prerequisites

1. **Render.com Account**: Sign up at https://render.com
2. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, etc.)
3. **Environment Variables**: Prepare your environment variables

## Deployment Steps

### Method 1: Web Dashboard Deployment

1. **Login to Render.com**
   - Go to https://render.com and sign in
   - Connect your GitHub/GitLab account

2. **Create New Web Service**
   - Click "New +" → "Web Service"
   - Connect your repository
   - Select the repository containing your WiggyZ backend

3. **Configure Service**
   - **Name**: `wiggyz-backend`
   - **Environment**: `Node`
   - **Region**: Choose closest to your users
   - **Branch**: `main` (or your deployment branch)
   - **Root Directory**: `backend`
   - **Build Command**: `npm install && npm run build`
   - **Start Command**: `npm start`

4. **Environment Variables**
   Add these environment variables in the Render dashboard:
   ```
   NODE_ENV=production
   PORT=10000
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   JWT_REFRESH_SECRET=your_jwt_secret
   RAZORPAY_KEY_ID=your_razorpay_key_id
   RAZORPAY_KEY_SECRET=your_razorpay_key_secret
   ```

5. **Deploy**
   - Click "Create Web Service"
   - Render will automatically build and deploy your application

### Method 2: render.yaml Configuration

If you have a `render.yaml` file in your repository root, Render will automatically use it:

```yaml
services:
  - type: web
    name: wiggyz-backend
    env: node
    plan: free
    buildCommand: npm install && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
    healthCheckPath: /health
```

## Post-Deployment Configuration

### 1. Get Your Deployment URL

After deployment, Render will provide a URL like:
```
https://wiggyz-backend.onrender.com
```

### 2. Test the Deployment

Test these endpoints:
- **Health Check**: `https://your-app.onrender.com/health`
- **API Base**: `https://your-app.onrender.com/api/v1/`

### 3. Update Flutter App Configuration

Update `frontend/lib/core/api/api_config.dart`:

```dart
class ApiConfig {
  static String get baseUrl {
    // Replace with your Render deployment URL
    return 'https://wiggyz-backend.onrender.com/api/v1';
  }
  // ... rest of the configuration
}
```

## Environment Variables Setup

### Required Variables

```bash
NODE_ENV=production
PORT=10000
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
JWT_REFRESH_SECRET=your-secret-key
RAZORPAY_KEY_ID=your-razorpay-key
RAZORPAY_KEY_SECRET=your-razorpay-secret
```

### Setting Variables in Render

1. Go to your service dashboard
2. Click "Environment" tab
3. Add each variable with its value
4. Click "Save Changes"

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check build logs in Render dashboard
   - Ensure all dependencies are in package.json
   - Verify TypeScript compilation

2. **Environment Variable Issues**
   - Double-check variable names and values
   - Ensure no trailing spaces in values
   - Verify Supabase URL and keys

3. **Port Issues**
   - Render automatically sets PORT environment variable
   - Ensure your app listens on `process.env.PORT || 10000`

4. **Health Check Failures**
   - Ensure `/health` endpoint returns 200 status
   - Check if endpoint is accessible

### Logs and Monitoring

- **Build Logs**: Available during deployment
- **Application Logs**: Real-time logs in dashboard
- **Metrics**: CPU, memory usage, response times

## Performance Considerations

### Free Tier Limitations

- **Sleep Mode**: Apps sleep after 15 minutes of inactivity
- **Spin-up Time**: ~30 seconds to wake up from sleep
- **Monthly Hours**: 750 hours/month (enough for development)

### Optimization Tips

1. **Keep Warm**: Use a service like UptimeRobot to ping your app
2. **Efficient Queries**: Optimize database queries
3. **Caching**: Implement appropriate caching strategies

## Upgrade Options

For production use, consider upgrading to:
- **Starter Plan**: $7/month, no sleep mode
- **Standard Plan**: $25/month, more resources
- **Pro Plan**: $85/month, high performance

## Support

For deployment issues:
1. Check Render dashboard logs
2. Review environment variables
3. Test endpoints manually
4. Check Supabase connectivity

## Comparison with Vercel

| Feature | Render.com | Vercel |
|---------|------------|--------|
| Always-on | ✅ Yes | ❌ Serverless |
| Public APIs | ✅ Yes | ⚠️ May require auth |
| Free Tier | ✅ 750 hours | ✅ Generous |
| Cold Starts | ❌ Only after sleep | ✅ Optimized |
| Node.js Support | ✅ Native | ✅ Serverless |

---

**Last Updated**: July 2025
**Version**: 1.0.0

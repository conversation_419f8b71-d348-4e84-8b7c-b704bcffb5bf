import { Request, Response, NextFunction } from 'express';
import { supabase } from '../config/supabase';
import { verifyAccessToken, TokenPayload } from '../utils/jwt-rs256';
import { logger } from '../utils/logger';
import { cacheVerification, getCachedVerification, isTokenBlacklisted } from '../utils/tokenCache';
import { isRedisConnectionAvailable } from '../config/redis';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

// Generate a hash of the token for session isolation
const generateTokenHash = (token: string): string => {
  return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
};

// Function to verify Supabase JWT tokens
const verifySupabaseToken = async (token: string): Promise<{ userId: string; email?: string } | null> => {
  try {
    // Use Supabase client to verify the token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      logger.debug(`Supabase token verification failed: ${error?.message || 'No user found'}`);
      return null;
    }

    return {
      userId: user.id,
      email: user.email
    };
  } catch (err) {
    logger.debug(`Supabase token verification error: ${err instanceof Error ? err.message : String(err)}`);
    return null;
  }
};

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
        role: string;
        isActive: boolean;
        isVerified: boolean;
      };
    }
  }
}

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];

    // Enhanced logging for claim requests
    if (req.originalUrl.includes('/claim-daily')) {
      logger.debug(`🔍 [AUTH] Claim request token analysis:`);
      logger.debug(`🔍 [AUTH] Full auth header: ${authHeader.substring(0, 60)}...`);
      logger.debug(`🔍 [AUTH] Extracted token length: ${token.length}`);
      logger.debug(`🔍 [AUTH] Token preview: ${token.substring(0, 50)}...`);
      logger.debug(`🔍 [AUTH] Token ends with: ...${token.substring(token.length - 20)}`);

      // Check JWT structure
      const parts = token.split('.');
      logger.debug(`🔍 [AUTH] JWT parts count: ${parts.length} (should be 3)`);
      if (parts.length === 3) {
        logger.debug(`🔍 [AUTH] Header length: ${parts[0].length}`);
        logger.debug(`🔍 [AUTH] Payload length: ${parts[1].length}`);
        logger.debug(`🔍 [AUTH] Signature length: ${parts[2].length}`);
      }
    }
    
    // Check if token is blacklisted first (for revoked tokens)
    const isBlacklisted = await isTokenBlacklisted(token);
    if (isBlacklisted) {
      logger.warn(`Attempt to use blacklisted token for request to ${req.originalUrl}`);
      return res.status(401).json({ error: 'Token has been revoked' });
    }
    
    // Log Redis availability for debugging
    if (process.env.NODE_ENV === 'development') {
      const redisAvailable = isRedisConnectionAvailable();
      logger.debug(`Auth middleware using ${redisAvailable ? 'Redis' : 'memory fallback'} for token cache`);
    }
    
    // Try to verify as backend token first, then as Supabase token
    let payload = await verifyAccessToken(token);
    let isSupabaseToken = false;
    let supabaseUser = null;

    if (!payload) {
      // If backend token verification fails, try Supabase token verification
      supabaseUser = await verifySupabaseToken(token);
      if (supabaseUser) {
        isSupabaseToken = true;
        logger.debug(`Using Supabase token for user ${supabaseUser.userId}`);
      } else {
        logger.warn(`Both backend and Supabase token verification failed for request to ${req.originalUrl}`);
        logger.debug(`Failed token (first 20 chars): ${token.substring(0, 20)}...`);
        return res.status(401).json({ error: 'Invalid or expired token' });
      }
    }

    // SECURITY FIX: Enhanced token expiration validation for backend tokens
    if (payload) {
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < now) {
        logger.security(`Expired token detected for user ${payload.userId}, exp: ${payload.exp}, now: ${now}, request: ${req.originalUrl}`);
        return res.status(401).json({ error: 'Token has expired' });
      }

      // Additional validation for critical operations (match creation, payments)
      if (req.originalUrl.includes('/matches') && (req.method === 'POST' || req.originalUrl.includes('/join'))) {
        const timeUntilExpiry = payload.exp ? (payload.exp - now) : 0;
        if (timeUntilExpiry < 300) { // Less than 5 minutes until expiry
          logger.security(`Token expiring soon for critical operation - User: ${payload.userId}, Time left: ${timeUntilExpiry}s, Request: ${req.originalUrl}`);
          return res.status(401).json({
            error: 'Token expiring soon, please refresh',
            code: 'TOKEN_EXPIRING'
          });
        }
      }
    }

    // Log token validation success for profile endpoints (debugging)
    if (req.originalUrl.includes('/profile')) {
      const expiryDate = payload?.exp ? new Date(payload.exp * 1000) : 'Unknown';
      const userIdForLog = payload?.userId || supabaseUser?.userId;
      logger.debug(`Token validation successful for profile request - User: ${userIdForLog}, Expires: ${expiryDate}`);
    }

    const userId = payload?.userId || supabaseUser?.userId;
    const tokenId = payload?.jti; // JWT ID for more specific caching (only for backend tokens)

    if (!userId) {
      logger.warn(`No user ID found in token for request to ${req.originalUrl}`);
      return res.status(401).json({ error: 'Invalid token - no user ID' });
    }
    
    // For Supabase tokens, we always need to fetch user data from database
    // For backend tokens, prioritize token claims if they exist
    if (!isSupabaseToken && payload &&
        payload.isVerified !== undefined &&
        payload.role !== undefined) {

      // Attach user to request from token claims
      req.user = {
        userId: payload.userId,
        role: payload.role,
        isActive: payload.isVerified, // Use isVerified as isActive
        isVerified: payload.isVerified
      };

      // For non-sensitive operations, we can use the token claims alone
      // For features requiring fresh data, continue to the full verification
      if (!req.originalUrl.includes('/profile') &&
          !req.originalUrl.includes('/wallet') &&
          !req.originalUrl.includes('/shop/purchase')) {

        // Update request count for this token (analytics)
        if (req.originalUrl !== '/api/v1/auth/ping') {
          // Don't log ping requests to reduce noise
          logger.debug(`Using token claims for lightweight auth, user ${userId}, endpoint ${req.originalUrl}`);
        }

        return next();
      }
    }
    
    // ENHANCED SESSION ISOLATION: Check cache with proper token-specific keys
    const tokenHash = generateTokenHash(token);
    let cachedUserData = null;

    if (!isSupabaseToken && tokenId && payload) {
      // For backend tokens, use JWT ID
      cachedUserData = await getCachedVerification(userId, tokenId);
    } else if (isSupabaseToken) {
      // For Supabase tokens, use token hash for session isolation
      cachedUserData = await getCachedVerification(userId, undefined, tokenHash);
    }

    if (cachedUserData) {
      // SECURITY FIX: Validate cache freshness and token expiration even with cached data
      const now = Math.floor(Date.now() / 1000);
      const cacheAge = cachedUserData._meta?.cached_at ? (Date.now() - cachedUserData._meta.cached_at) / 1000 : 0;

      // For critical operations, require fresh verification even if cached
      const isCriticalOperation = req.originalUrl.includes('/matches') &&
        (req.method === 'POST' || req.originalUrl.includes('/join') || req.originalUrl.includes('/submit-result'));

      if (isCriticalOperation && cacheAge > 60) { // Require fresh data for critical ops older than 1 minute
        logger.debug(`Bypassing cache for critical operation - User: ${userId}, Cache age: ${cacheAge}s`);
      } else {
        // Use cached user data
        req.user = {
          userId: userId,
          role: cachedUserData.role || (payload?.role),
          isActive: cachedUserData.is_verified,
          isVerified: cachedUserData.is_verified
        };

        logger.debug(`Using cached verification for user ${userId}, cache age: ${cacheAge}s, token type: ${isSupabaseToken ? 'Supabase' : 'Backend'}`);
        return next();
      }
    }
    
    // Cache miss, verify with Supabase
    const { data: userData, error } = await supabase
      .from('users')
      .select('id, role, is_verified')
      .eq('id', userId)
      .single();
      
    if (error) {
      logger.error(`Database error for user ${userId}: ${JSON.stringify(error)}`);
      return res.status(401).json({ error: 'Error retrieving user data' });
    }
    
    if (!userData) {
      logger.error(`User ${userId} not found in database`);
      return res.status(401).json({ error: 'User not found' });
    }
    
    // Check if user is blocked
    if (userData.is_blocked) {
      logger.warn(`Blocked user ${userId} attempted to access ${req.originalUrl}`);
      return res.status(403).json({ 
        error: 'Account blocked', 
        message: 'Your account has been blocked. Please contact support.'
      });
    }
    
    // Store user status in cache to reduce database lookups
    const userDataForCache = {
      ...userData,
      // Include any additional security claims from the token
      role: userData.role || payload?.role || 'user',
      // Convert from snake_case DB field to camelCase for consistency
      isActive: userData.is_verified, // Use is_verified as is_active
      isVerified: userData.is_verified,
      // Optional: record when this verification happened
      verifiedAt: Date.now()
    };
    
    // ENHANCED SESSION ISOLATION: Cache verification result with proper token-specific keys
    if (!isSupabaseToken && tokenId && payload) {
      // For backend tokens, use JWT ID
      await cacheVerification(userId, userDataForCache, tokenId);
      logger.debug(`Cached user verification in Redis for user ${userId} with token ID ${tokenId}`);
    } else if (isSupabaseToken) {
      // For Supabase tokens, use token hash for session isolation
      await cacheVerification(userId, userDataForCache, undefined, tokenHash);
      logger.debug(`Cached Supabase token verification for user ${userId} with token hash ${tokenHash}`);
    }

    // Attach user to request
    req.user = {
      userId: userId,
      role: userData.role,
      isActive: userData.is_verified, // Use is_verified as is_active
      isVerified: userData.is_verified
    };
    
    // Update last active timestamp for user (non-blocking)
    updateLastActive(userId).catch(error => {
      logger.error(`Error updating last active timestamp: ${error instanceof Error ? error.message : String(error)}`);
    });
    
    return next();
  } catch (err) {
    logger.error(`Auth middleware error: ${err instanceof Error ? err.message : String(err)}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

// Role-based authorization middleware factory
export const authorize = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Permission denied' });
    }

    // No need to check active/verified status here as the authenticate middleware
    // already performs these checks before this middleware runs

    next();
  };
};

/**
 * Helper function to update the user's last active timestamp
 * This is called asynchronously to avoid blocking the request
 */
async function updateLastActive(userId: string): Promise<void> {
  try {
    await supabase
      .from('users')
      .update({ last_active: new Date() })
      .eq('id', userId);
  } catch (error) {
    logger.error(`Error updating last_active for user ${userId}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// Export authenticate as authMiddleware for backward compatibility
export const authMiddleware = authenticate;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:wiggyz_app/features/wallet/providers/wallet_provider.dart';
import 'package:wiggyz_app/services/razorpay_service.dart';
import 'package:wiggyz_app/providers/auth_provider.dart';
import 'package:uuid/uuid.dart';

class AddMoneyScreen extends StatefulWidget {
  const AddMoneyScreen({super.key});

  @override
  State<AddMoneyScreen> createState() => _AddMoneyScreenState();
}

class _AddMoneyScreenState extends State<AddMoneyScreen> {
  final TextEditingController _amountController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late RazorpayService _razorpayService;
  final Uuid _uuid = const Uuid();
  bool _isProcessing = false;

  final List<String> _quickAmounts = ['₹100', '₹200', '₹500', '₹1000', '₹2000'];
  final List<PaymentMethodModel> _paymentMethods = [
    PaymentMethodModel(
      id: 'upi',
      name: 'UPI',
      icon: 'assets/icons/upi.png',
      isAsset: true,
      iconData: Icons.account_balance,
    ),
    PaymentMethodModel(
      id: 'card',
      name: 'Credit/Debit Card',
      icon: 'assets/icons/card.png',
      isAsset: true,
      iconData: Icons.credit_card,
    ),
    PaymentMethodModel(
      id: 'netbanking',
      name: 'Net Banking',
      icon: 'assets/icons/bank.png',
      isAsset: true,
      iconData: Icons.account_balance,
    ),
    PaymentMethodModel(
      id: 'wallet',
      name: 'Other Wallets',
      icon: 'assets/icons/wallet.png',
      isAsset: true,
      iconData: Icons.account_balance_wallet,
    ),
  ];
  String _selectedPaymentMethod = 'upi';

  @override
  void initState() {
    super.initState();
    _razorpayService = RazorpayService();
    _razorpayService.initialize();

    // Add listener for real-time payment summary updates
    _amountController.addListener(_onAmountChanged);

    // Fetch wallet details when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final walletProvider = Provider.of<WalletProvider>(
        context,
        listen: false,
      );
      walletProvider.fetchWalletDetails();
    });
  }

  void _onAmountChanged() {
    // Trigger UI rebuild when amount changes
    setState(() {
      // The payment summary will automatically recalculate in _buildPaymentSummary
    });
  }

  String _formatCurrency(double amount) {
    if (amount == 0.0) return '₹0';
    return '₹${amount.toStringAsFixed(2)}';
  }

  @override
  void dispose() {
    _amountController.removeListener(_onAmountChanged);
    _amountController.dispose();
    _razorpayService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final walletProvider = Provider.of<WalletProvider>(context);

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[100],
      appBar: AppBar(
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFFFFCC00), Color(0xFFFF9500)],
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Add Money',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
      body: SafeArea(
        child: RefreshIndicator(
          color: const Color(0xFFFFCC00),
          onRefresh: () async {
            await walletProvider.fetchWalletDetails();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Current Balance Card
                    _buildCurrentBalanceCard(isDarkMode, walletProvider),

                    const SizedBox(height: 24),

                    // Amount Input Section
                    Text(
                      'Enter Amount',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Amount Text Field
                    _buildAmountTextField(isDarkMode),

                    const SizedBox(height: 16),

                    // Quick Amount Selection
                    _buildQuickAmountSelector(isDarkMode),

                    const SizedBox(height: 24),

                    // Payment Method Section
                    Text(
                      'Select Payment Method',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Payment Methods List
                    _buildPaymentMethodsList(isDarkMode),

                    const SizedBox(height: 24),

                    // Payment Summary
                    _buildPaymentSummary(isDarkMode),

                    const SizedBox(height: 24),

                    // Add Money Button
                    _buildAddMoneyButton(isDarkMode),

                    const SizedBox(height: 16),

                    // Terms and Conditions
                    _buildTermsText(isDarkMode),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentBalanceCard(
    bool isDarkMode,
    WalletProvider walletProvider,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Balance',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              walletProvider.isLoading && walletProvider.walletData == null
                  ? const CircularProgressIndicator(
                    color: Color(0xFFFFCC00),
                    strokeWidth: 2,
                  )
                  : walletProvider.errorMessage != null &&
                      walletProvider.walletData == null
                  ? Text(
                    'Error loading balance',
                    style: GoogleFonts.poppins(fontSize: 16, color: Colors.red),
                  )
                  : Text(
                    '₹${walletProvider.walletData?['balance']?.toStringAsFixed(2) ?? '0.00'}',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      size: 12,
                      color: Colors.green,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Verified Account',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAmountTextField(bool isDarkMode) {
    return TextFormField(
      controller: _amountController,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')), // Allow decimals with up to 2 decimal places
      ],
      style: GoogleFonts.poppins(
        fontSize: 16,
        color: isDarkMode ? Colors.white : Colors.black,
        fontWeight: FontWeight.w500,
      ),
      decoration: InputDecoration(
        filled: true,
        fillColor: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
        prefixIcon: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Text(
            '₹',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFFFFCC00),
            ),
          ),
        ),
        prefixIconConstraints: const BoxConstraints(minWidth: 0, minHeight: 0),
        hintText: '0',
        hintStyle: GoogleFonts.poppins(
          color: isDarkMode ? Colors.grey[600] : Colors.grey[400],
          fontSize: 16,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFFFCC00), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter amount';
        }
        final amount = int.tryParse(value);
        if (amount == null) {
          return 'Invalid amount';
        }
        if (amount < 25) {
          return 'Minimum amount is ₹25';
        }
        if (amount > 10000) {
          return 'Maximum amount is ₹10,000';
        }
        return null;
      },
    );
  }

  Widget _buildQuickAmountSelector(bool isDarkMode) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children:
          _quickAmounts.map((amount) {
            return InkWell(
              onTap: () {
                _amountController.text = amount.substring(1); // Remove ₹ symbol
                // The listener will automatically trigger setState
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  amount,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }

  Widget _buildPaymentMethodsList(bool isDarkMode) {
    return Column(
      children:
          _paymentMethods.map((method) {
            final isSelected = _selectedPaymentMethod == method.id;
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedPaymentMethod = method.id;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          isSelected
                              ? const Color(0xFFFFCC00)
                              : isDarkMode
                              ? Colors.grey[800]!
                              : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? const Color(0xFFFFCC00).withOpacity(0.1)
                                  : isDarkMode
                                  ? Colors.grey[800]!
                                  : Colors.grey[100]!,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          method.iconData,
                          color:
                              isSelected
                                  ? const Color(0xFFFFCC00)
                                  : Colors.grey,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        method.name,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        isSelected ? Icons.check_circle : Icons.circle_outlined,
                        color:
                            isSelected ? const Color(0xFFFFCC00) : Colors.grey,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }



  Widget _buildPaymentSummary(bool isDarkMode) {
    // Calculate transaction fee, GST on fee, and amounts with real-time updates
    double baseAmount = 0.0;
    double transactionFee = 0.0;
    double gstOnFee = 0.0;
    double totalDeductions = 0.0;
    double amountToWallet = 0.0;
    double amountCharged = 0.0;
    bool isValidAmount = false;
    String? validationMessage;

    if (_amountController.text.isNotEmpty) {
      baseAmount = double.tryParse(_amountController.text) ?? 0.0;

      if (baseAmount > 0) {
        isValidAmount = true;
        transactionFee = baseAmount * 0.02; // 2% transaction fee
        gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
        totalDeductions = transactionFee + gstOnFee;
        amountCharged = baseAmount; // User pays the amount they entered
        amountToWallet = baseAmount - totalDeductions; // Net amount credited to wallet

        // Validation checks
        if (baseAmount < 25) {
          validationMessage = 'Minimum amount is ₹25';
        } else if (baseAmount > 10000) {
          validationMessage = 'Maximum amount is ₹10,000';
        } else if (amountToWallet < 20) {
          validationMessage = 'Net amount after fees should be at least ₹20';
        }
      }
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Summary',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          if (validationMessage != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    size: 16,
                    color: Colors.orange,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      validationMessage!,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Base Amount',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
              ),
              Text(
                _formatCurrency(baseAmount),
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Transaction Fee (2%)',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
              ),
              Text(
                _formatCurrency(transactionFee),
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'GST on Fee (18%)',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
              ),
              Text(
                _formatCurrency(gstOnFee),
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Deductions',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                ),
              ),
              Text(
                _formatCurrency(totalDeductions),
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          const Divider(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Amount to be Added to Wallet',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFFFCC00),
                ),
              ),
              Text(
                _formatCurrency(amountToWallet),
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFFFCC00),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Amount Charged',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              Text(
                _formatCurrency(amountCharged),
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddMoneyButton(bool isDarkMode) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed:
            _isProcessing
                ? null
                : () {
                  if (_formKey.currentState!.validate()) {
                    _processPayment();
                  }
                },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFCC00),
          foregroundColor: Colors.black,
          disabledBackgroundColor:
              isDarkMode ? Colors.grey[800] : Colors.grey[300],
          disabledForegroundColor:
              isDarkMode ? Colors.grey[600] : Colors.grey[500],
          elevation: 0,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child:
            _isProcessing
                ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.black,
                  ),
                )
                : Text(
                  'Add Money',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
      ),
    );
  }

  Widget _buildTermsText(bool isDarkMode) {
    return Center(
      child: Text(
        'By continuing, you agree to our Terms & Conditions',
        style: GoogleFonts.poppins(
          fontSize: 12,
          color: isDarkMode ? Colors.grey[500] : Colors.grey[600],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _processPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final baseAmount = double.parse(_amountController.text);
      final transactionFee = baseAmount * 0.02; // 2% transaction fee
      final gstOnFee = transactionFee * 0.18; // 18% GST on transaction fee only
      final totalDeductions = transactionFee + gstOnFee;
      final amountToWallet = baseAmount - totalDeductions; // Net amount to be credited to wallet
      final amountCharged = baseAmount; // User pays the amount they entered

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.user;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Process payment with Razorpay using the user-entered amount (not including fees)
      final result = await _razorpayService.processWalletTopUp(
        context: context,
        amount: amountCharged, // Charge the user-entered amount
        currency: 'INR',
        userEmail: user.email ?? '',
        userPhone: user.phone ?? '',
        userName: user.name ?? 'User',
        transactionId: null, // Let backend generate the transaction ID
      );

      // Refresh wallet details after successful payment
      final walletProvider = Provider.of<WalletProvider>(context, listen: false);
      await walletProvider.fetchWalletDetails();

      setState(() {
        _isProcessing = false;
      });

      // Show success dialog
      _showSuccessDialog(result);
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      // Show error dialog with improved error message
      String errorMessage = e.toString();
      if (errorMessage.contains('Payment not supported on web platform')) {
        errorMessage = 'Payment is not supported on web platform. Please use the mobile app.';
      } else if (errorMessage.contains('MissingPluginException')) {
        errorMessage = 'Payment service is not available. Please try again or contact support.';
      } else if (errorMessage.contains('Could not find the \'payment_gateway\' column')) {
        errorMessage = 'Payment system is being updated. Please try again in a few minutes.';
      } else if (errorMessage.contains('Internal server error')) {
        errorMessage = 'Payment service is temporarily unavailable. Please try again.';
      }

      _showErrorDialog(errorMessage);
    }
  }

  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return Dialog(
          backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.check_circle, color: Colors.green, size: 64),
                const SizedBox(height: 16),
                Text(
                  'Payment Successful!',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Your wallet has been successfully recharged with ₹${_amountController.text}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Transaction ID',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                      ),
                    ),
                    Text(
                      'WIG${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Date & Time',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                      ),
                    ),
                    Text(
                      '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}, ${DateTime.now().hour}:${DateTime.now().minute}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Payment Method',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                      ),
                    ),
                    Text(
                      _paymentMethods
                          .firstWhere(
                            (method) => method.id == _selectedPaymentMethod,
                          )
                          .name,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(context); // Close dialog

                      // Refresh wallet data and transactions
                      final walletProvider = Provider.of<WalletProvider>(context, listen: false);
                      await walletProvider.fetchWalletDetails();
                      await walletProvider.fetchWalletTransactions(page: 1, limit: 50);

                      Navigator.pop(context); // Go back to wallet screen
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFFCC00),
                      foregroundColor: Colors.black,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Back to Wallet',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return Dialog(
          backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 64),
                const SizedBox(height: 16),
                Text(
                  'Payment Failed',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  error,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: isDarkMode ? Colors.grey[300] : Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context); // Close dialog
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Try Again',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class PaymentMethodModel {
  final String id;
  final String name;
  final String icon;
  final bool isAsset;
  final IconData iconData;

  PaymentMethodModel({
    required this.id,
    required this.name,
    required this.icon,
    this.isAsset = false,
    required this.iconData,
  });
}

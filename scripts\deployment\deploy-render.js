#!/usr/bin/env node

/**
 * Render.com Deployment Helper for WiggyZ Backend
 * Provides deployment guidance and configuration validation
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

class RenderDeployment {
  constructor() {
    this.rootDir = path.resolve(__dirname, '../..');
    this.backendDir = path.join(this.rootDir, 'backend');
  }

  log(message, type = 'info') {
    const emoji = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
    console.log(`${emoji} ${message}`);
  }

  validateConfiguration() {
    this.log('Validating Render deployment configuration...');

    // Check if package.json has correct scripts
    const packageJsonPath = path.join(this.backendDir, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      this.log('package.json not found in backend directory', 'error');
      return false;
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Check required scripts
    const requiredScripts = ['start', 'build'];
    for (const script of requiredScripts) {
      if (!packageJson.scripts || !packageJson.scripts[script]) {
        this.log(`Missing required script: ${script}`, 'error');
        return false;
      }
    }

    this.log('Package.json configuration is valid', 'success');

    // Check if render.yaml exists
    const renderConfigPath = path.join(this.backendDir, 'render.yaml');
    if (fs.existsSync(renderConfigPath)) {
      this.log('render.yaml configuration found', 'success');
    } else {
      this.log('render.yaml not found (optional)', 'warn');
    }

    return true;
  }

  async testEndpoint(url) {
    this.log(`Testing endpoint: ${url}`);
    
    return new Promise((resolve) => {
      const req = https.get(url, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          if (res.statusCode === 200) {
            this.log(`✅ Endpoint accessible (${res.statusCode})`, 'success');
            this.log(`Response: ${data.substring(0, 100)}...`, 'info');
            resolve(true);
          } else if (res.statusCode === 401) {
            this.log(`❌ Endpoint requires authentication (${res.statusCode})`, 'error');
            resolve(false);
          } else {
            this.log(`⚠️ Endpoint returned status ${res.statusCode}`, 'warn');
            resolve(false);
          }
        });
      });
      
      req.on('error', (error) => {
        this.log(`❌ Test failed: ${error.message}`, 'error');
        resolve(false);
      });
      
      req.setTimeout(10000, () => {
        this.log('❌ Test timeout', 'error');
        req.destroy();
        resolve(false);
      });
    });
  }

  displayDeploymentInstructions() {
    console.log('\n🚀 Render.com Deployment Instructions\n');
    
    console.log('1. 📋 Prerequisites:');
    console.log('   • Render.com account (https://render.com)');
    console.log('   • Git repository with your code');
    console.log('   • Environment variables ready\n');
    
    console.log('2. 🔧 Deployment Steps:');
    console.log('   • Login to Render.com dashboard');
    console.log('   • Click "New +" → "Web Service"');
    console.log('   • Connect your Git repository');
    console.log('   • Configure service settings:\n');
    
    console.log('     📝 Service Configuration:');
    console.log('     • Name: wiggyz-backend');
    console.log('     • Environment: Node');
    console.log('     • Region: Choose closest to users');
    console.log('     • Root Directory: backend');
    console.log('     • Build Command: npm install && npm run build');
    console.log('     • Start Command: npm start\n');
    
    console.log('     🔐 Environment Variables:');
    console.log('     • NODE_ENV=production');
    console.log('     • PORT=10000');
    console.log('     • SUPABASE_URL=your_supabase_url');
    console.log('     • SUPABASE_KEY=your_supabase_key');
    console.log('     • JWT_REFRESH_SECRET=your_jwt_secret');
    console.log('     • RAZORPAY_KEY_ID=your_razorpay_key_id');
    console.log('     • RAZORPAY_KEY_SECRET=your_razorpay_key_secret\n');
    
    console.log('3. 🧪 After Deployment:');
    console.log('   • Test health endpoint: https://your-app.onrender.com/health');
    console.log('   • Test API base: https://your-app.onrender.com/api/v1/');
    console.log('   • Update Flutter app configuration\n');
    
    console.log('4. 📱 Update Flutter App:');
    console.log('   • Edit frontend/lib/core/api/api_config.dart');
    console.log('   • Replace baseUrl with your Render URL');
    console.log('   • Example: https://wiggyz-backend.onrender.com/api/v1\n');
    
    console.log('📚 For detailed instructions, see: docs/RENDER_DEPLOYMENT.md\n');
  }

  async run() {
    console.log('🌐 WiggyZ Render.com Deployment Helper\n');
    
    // Validate configuration
    if (!this.validateConfiguration()) {
      this.log('Configuration validation failed', 'error');
      return false;
    }
    
    // Display deployment instructions
    this.displayDeploymentInstructions();
    
    // Check if user provided a URL to test
    const testUrl = process.argv[2];
    if (testUrl) {
      console.log('🧪 Testing provided URL...\n');
      const healthUrl = testUrl.endsWith('/health') ? testUrl : `${testUrl}/health`;
      const isAccessible = await this.testEndpoint(healthUrl);
      
      if (isAccessible) {
        console.log('\n🎉 Deployment is accessible and working!');
        console.log(`✅ Backend URL: ${testUrl}`);
        console.log(`✅ API Base URL: ${testUrl}/api/v1`);
        console.log('\n📱 Next step: Update Flutter app configuration');
        return true;
      } else {
        console.log('\n❌ Deployment test failed');
        console.log('💡 Check Render dashboard logs for issues');
        return false;
      }
    }
    
    console.log('💡 Run with URL to test: node scripts/deployment/deploy-render.js https://your-app.onrender.com');
    return true;
  }
}

// CLI Interface
if (require.main === module) {
  const deployer = new RenderDeployment();
  
  deployer.run()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('\n💥 Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = RenderDeployment;
